# 🪟 Funzionalità Windows - Agevolami PM

## 🚀 Avvio Automatico

### Funzionalità
- **Avvio con Windows**: L'applicazione può essere configurata per avviarsi automaticamente all'avvio di Windows
- **Avvio Minimizzato**: Opzione per avviare l'app minimizzata nell'area di notifica
- **Gestione Registro**: Utilizza il registro di Windows per configurare l'avvio automatico

### Configurazione
1. Apri **Impostazioni** → **Impostazioni Windows**
2. Attiva **"Avvia con Windows"**
3. Opzionale: Attiva **"Avvia Minimizzato"**
4. Salva le impostazioni

### Note Tecniche
- Richiede privilegi utente per modificare il registro
- Utilizza la chiave `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run`
- Supporta sia versioni compilate (.exe) che di sviluppo

---

## 🔔 Notifiche Desktop

### Caratteristiche
- **Toast Notifications**: Notifiche native Windows che appaiono come WhatsApp
- **Priorità Multiple**: Normale, Alta, Urgente
- **Notifiche Personalizzate**: Specifiche per scadenze, progetti, etc.
- **Orari Silenziosi**: Configurabili per non disturbare

### Tipi di Notifica

#### Notifiche di Sistema
```
✅ Avvio applicazione
📧 Report inviati
⚙️ Configurazione aggiornata
```

#### Notifiche Scadenze
```
🚨 SCADENZA SCADUTA!        (giorni < 0)
⚠️ SCADENZA IMMINENTE!     (oggi/domani)  
⏰ Scadenza Ravvicinata     (2-3 giorni)
📅 Promemoria Scadenza     (4-15 giorni)
```

#### Notifiche Progetti
```
✅ Nuovo progetto creato
🎉 Progetto completato
📝 Progetto aggiornato
📅 Nuova scadenza aggiunta
```

### Configurazione Notifiche

#### Impostazioni Base
- **Notifiche Desktop**: On/Off globale
- **Suoni Notifiche**: Abilita/disabilita suoni
- **Filtro Priorità**: Mostra solo notifiche di priorità selezionata

#### Orari Silenziosi
- **Abilita Orari Silenziosi**: On/Off
- **Inizio Silenzioso**: Es. 22:00
- **Fine Silenzioso**: Es. 08:00
- **Eccezioni**: Le notifiche CRITICHE ignorano gli orari silenziosi

#### Filtri Priorità
- **Tutte**: Mostra tutte le notifiche
- **Normali e superiori**: Esclude notifiche a bassa priorità
- **Solo importanti**: Solo notifiche ad alta priorità
- **Solo urgenti**: Solo notifiche critiche/urgenti

---

## 🎯 Integrazione Sistema

### Alert Service Enhanced
Il servizio alert è stato potenziato per utilizzare le notifiche Windows:

```python
# Verifica priorità
if alert_priority >= filter_priority:
    # Controlla orari silenziosi
    if not is_quiet_hours() or is_critical:
        send_desktop_notification()
```

### Gestione Intelligente
- **Rispetto Orari**: Non disturba negli orari silenziosi (tranne emergenze)
- **Filtri Intelligenti**: Mostra solo notifiche rilevanti
- **Fallback Graceful**: Se le notifiche Windows falliscono, usa log

---

## 🧪 Test e Debug

### Script di Test
Esegui il test delle funzionalità:

```bash
python test_windows_features.py
```

### Test Inclusi
1. **Rilevamento Piattaforma**: Verifica se siamo su Windows
2. **Stato Startup**: Controlla configurazione avvio automatico
3. **Test Notifiche**: Prova diverse tipologie di notifiche
4. **Sistema Completo**: Test integrazione completa
5. **Interattivo**: Permette di testare l'avvio automatico

### Demo Notifiche
```bash
# Il script include una demo interattiva che mostra:
# - Notifica normale
# - Notifica importante  
# - Notifica urgente
# - Notifica scadenza specifica
```

---

## 🔧 Requisiti Tecnici

### Dipendenze
```
plyer>=2.1.0          # Notifiche cross-platform
win10toast>=0.9.0     # Toast notifications Windows
pywin32>=306          # API Windows per registro
```

### Compatibilità
- **Windows 10/11**: Supporto completo
- **Windows 8.1**: Supporto notifiche base
- **Altri OS**: Fallback a notifiche plyer

### Permessi
- **Registro Windows**: Per avvio automatico
- **Notifiche**: Automatico con toast notifications
- **Nessun UAC**: Non richiede privilegi amministratore

---

## 🛠️ Risoluzione Problemi

### Avvio Automatico Non Funziona
1. Verifica di essere su Windows
2. Controlla permessi utente
3. Verifica log per errori registro
4. Prova con "Esegui come amministratore"

### Notifiche Non Appaiono
1. Verifica impostazioni Windows (Centro notifiche)
2. Controlla filtri priorità nell'app
3. Verifica orari silenziosi
4. Controlla se `win10toast` e `plyer` sono installati

### Debug Avanzato
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Abilita debug per vedere tutti i tentativi di notifica
```

---

## 📱 Confronto con WhatsApp

### Somiglianze
- ✅ Toast notifications native
- ✅ Anteprima nel centro notifiche
- ✅ Icona e testo personalizzabili
- ✅ Durata configurabile

### Differenze
- ❌ Non supporta reply/azioni interattive
- ❌ Non ha chat/conversazioni
- ✅ Priorità multiple
- ✅ Integrazione con sistema alert

---

## 🎯 Casi d'Uso

### 1. Studio Professionale
```
Mattina: Notifica avvio + report giornaliero
Durante il giorno: Alert scadenze urgenti
Sera: Report finale + backup completato
```

### 2. Consulente Freelance
```
Filtro: Solo notifiche urgenti
Orari silenziosi: 20:00 - 08:00
Avvio automatico: Sì, minimizzato
```

### 3. Team Aziendale
```
Notifiche: Tutte abilitate
Priorità: Normali e superiori
Report programmati: Mattina e sera
```

---

## 🚀 Prossimi Sviluppi

### Funzionalità Future
- [ ] Notifiche interattive (azioni rapide)
- [ ] Integrazione con calendario Windows
- [ ] Notifiche push quando app è chiusa
- [ ] Temi personalizzabili per notifiche
- [ ] Statistiche utilizzo notifiche

### Miglioramenti Pianificati
- [ ] Preview allegati nelle notifiche
- [ ] Raggruppamento notifiche correlate
- [ ] Snooze personalizzabile
- [ ] Integrazione Cortana/assistenti vocali 