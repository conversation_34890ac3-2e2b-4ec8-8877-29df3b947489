# Google Services Setup Guide for Agevolami PM

This guide will help you set up both Google Drive backup and Google Calendar synchronization for Agevolami PM.

## Prerequisites

1. A Google account
2. Access to Google Cloud Console
3. Agevolami PM installed with Google services dependencies

## Step 1: Install Google Services Dependencies

Run this command to install the required packages:

```bash
pip install -r requirements-google.txt
```

## Step 2: Google Cloud Console Setup

### 2.1 Create a New Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click on the project dropdown at the top
3. Click "New Project"
4. Enter project name: "Agevolami PM Integration"
5. Click "Create"

### 2.2 Enable Required APIs

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for and enable these APIs:
   - **Google Drive API** (for backup functionality)
   - **Google Calendar API** (for deadline synchronization)

### 2.3 Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. If prompted, configure the OAuth consent screen:
   - Choose "External" user type
   - Fill in the required fields:
     - App name: "Agevolami PM"
     - User support email: your email
     - Developer contact: your email
   - Add scopes:
     - `https://www.googleapis.com/auth/drive.file`
     - `https://www.googleapis.com/auth/calendar`
4. For Application type, choose "Desktop application"
5. Name it "Agevolami PM Desktop"
6. Click "Create"
7. **Download the JSON file** - this contains your credentials

## Step 3: Configure Agevolami PM

### 3.1 Place Credentials File

1. In your Agevolami PM installation, navigate to the `data/config/` folder
2. Copy the downloaded JSON file there and rename it to `google_credentials.json`

### 3.2 Configure in Settings

1. Open Agevolami PM
2. Go to **Settings** (gear icon in the top right)
3. Navigate to the **"Servizi Google"** (Google Services) section
4. You'll see two subsections:

#### Google Drive Backup
1. Click **"Connetti Google Drive"**
2. Paste your credentials JSON content in the dialog
3. Click **"Autentica"**
4. A browser window will open for authorization
5. Grant the requested permissions
6. Return to Agevolami PM - you should see "Connesso" status
7. Configure your backup preferences:
   - Enable automatic backup
   - Set frequency (daily/weekly/monthly)
   - Choose what to include (logs, assets)

#### Google Calendar Integration
1. Click **"Connetti Calendar"**
2. Paste the same credentials JSON content in the dialog
3. Click **"Autentica"**
4. A browser window will open for authorization
5. Grant calendar permissions
6. Return to Agevolami PM - you should see "Connesso" status
7. Configure sync preferences:
   - Enable automatic sync
   - Enable priority color coding
   - Enable reminders

## Step 4: Test the Integration

### Test Google Drive
1. In Settings > Google Services, click **"Backup Manuale"**
2. Check that the backup completes successfully
3. Verify the backup appears in your Google Drive in a folder called "Agevolami PM"

### Test Google Calendar
1. Create a new deadline in Agevolami PM
2. Check that it appears in your Google Calendar in the "Agevolami PM" calendar
3. The deadline should be color-coded based on priority:
   - Green: Low priority
   - Blue: Medium priority
   - Orange: High priority
   - Red: Critical priority

## Features Available

### Google Drive Backup
- Automatic scheduled backups of your database
- Manual backup on demand
- Encrypted and compressed backup files
- Configurable retention periods
- Easy restore from backup

### Google Calendar Integration
- Automatic sync of deadlines to Google Calendar
- Color-coded events based on priority
- Email and popup reminders
- Cross-platform access (phone, tablet, web)
- Updates when deadline status changes

## Troubleshooting

### "Google services not available" Error
- Make sure you've installed the requirements: `pip install -r requirements-google.txt`
- Restart Agevolami PM after installing dependencies

### Authentication Fails
- Check that your `google_credentials.json` file is valid JSON
- Ensure you've enabled both APIs in Google Cloud Console
- Try removing the `google_token.pickle` file and re-authenticating

### Deadlines Not Syncing to Calendar
- Check that you have "Sync to Google Calendar" enabled when creating deadlines
- Verify the Google Calendar connection status in Settings
- Look at the application logs for sync error details

### Backup Fails
- Check your Google Drive storage space
- Verify Google Drive API is enabled in Cloud Console
- Check the logs for specific error messages

## File Locations

The integration creates these files in your `data/config/` folder:
- `google_credentials.json` - Your API credentials (keep secure!)
- `google_token.pickle` - OAuth tokens (auto-generated)

## Security Notes

- Keep your `google_credentials.json` file secure
- The OAuth tokens have limited scope (only calendar and drive file access)
- Backups are compressed and stored privately in your Google Drive
- No data is shared with third parties

## Support

If you encounter issues:
1. Check the application logs in the `logs/` folder
2. Verify your Google Cloud Console configuration
3. Try disconnecting and reconnecting the services
4. Contact support with specific error messages

---

**Note**: This integration allows Agevolami PM to work seamlessly with Google's ecosystem while keeping your data secure and under your control. 