#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the Italian Financial Incentives Monitoring System
"""

import sys
import os
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from core import AppConfig
from core.database import DatabaseManagerExtended
from core.services.web_scraping_service import WebScrapingService
from core.services.llm_service import LLMService
from core.services.incentives_monitoring_service import IncentivesMonitoringService
from core.models.incentive_models import IncentiveSource, MonitoringConfig

def test_web_scraping():
    """Test web scraping functionality"""
    print("🕷️ Testing Web Scraping Service...")
    
    config = {
        'sources': [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST],
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'request_delay_seconds': 1.0,
        'max_items_per_session': 10
    }
    
    scraper = WebScrapingService(config)
    
    # Test connection to each source
    for source in [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST]:
        print(f"  Testing connection to {source.value}...")
        success = scraper.test_connection(source)
        print(f"  ✅ {source.value}: {'Connected' if success else 'Failed'}")
    
    # Test scraping with keywords
    keywords = ['incentivi', 'finanziamenti', 'bandi', 'agevolazioni']
    print(f"\n  Scraping with keywords: {keywords}")
    
    try:
        items = scraper.scrape_all_sources(keywords)
        print(f"  ✅ Found {len(items)} items")
        
        if items:
            print("  📄 Sample items:")
            for i, item in enumerate(items[:3]):
                print(f"    {i+1}. {item.title[:50]}... (Source: {item.source.value})")
        
    except Exception as e:
        print(f"  ❌ Scraping error: {e}")
    
    scraper.close()
    print()

def test_llm_service():
    """Test LLM service functionality"""
    print("🤖 Testing LLM Service...")
    
    # Test without API key (should handle gracefully)
    config = {
        'openrouter_api_key': '',  # Empty for testing
        'llm_model': 'gpt-3.5-turbo',
        'llm_enabled': False
    }
    
    llm_service = LLMService(config)
    
    if not llm_service.enabled:
        print("  ⚠️ LLM service disabled (no API key)")
        print("  ℹ️ To test LLM functionality, add your OpenRouter API key to the config")
    else:
        print("  ✅ LLM service enabled")
        
        # Test connection
        connected = llm_service.test_connection()
        print(f"  {'✅' if connected else '❌'} Connection test: {'Success' if connected else 'Failed'}")
    
    print()

def test_database_integration():
    """Test database integration"""
    print("🗄️ Testing Database Integration...")
    
    try:
        # Create test database
        config = AppConfig()
        db_path = "test_incentives.db"
        
        # Remove existing test db
        if os.path.exists(db_path):
            os.remove(db_path)
        
        # Create new database
        config.database_path = db_path
        db_manager = DatabaseManagerExtended(db_path)
        
        print("  ✅ Database created successfully")
        
        # Check if incentives tables exist
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # Check incentives table
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='incentives'")
            incentives_table = cursor.fetchone()
            print(f"  {'✅' if incentives_table else '❌'} Incentives table: {'Exists' if incentives_table else 'Missing'}")
            
            # Check monitoring_sessions table
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='monitoring_sessions'")
            sessions_table = cursor.fetchone()
            print(f"  {'✅' if sessions_table else '❌'} Monitoring sessions table: {'Exists' if sessions_table else 'Missing'}")
        
        # Clean up
        db_manager.close()
        if os.path.exists(db_path):
            os.remove(db_path)
            
    except Exception as e:
        print(f"  ❌ Database error: {e}")
    
    print()

def test_monitoring_service():
    """Test the main monitoring service"""
    print("🎯 Testing Incentives Monitoring Service...")
    
    try:
        # Create test database
        config = AppConfig()
        db_path = "test_monitoring.db"
        
        # Remove existing test db
        if os.path.exists(db_path):
            os.remove(db_path)
        
        config.database_path = db_path
        db_manager = DatabaseManagerExtended(db_path)
        
        # Create monitoring config
        monitoring_config = {
            'incentives': {
                'enabled': True,
                'frequency': 'weekly',
                'keywords': ['incentivi', 'finanziamenti'],
                'sources': [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST],
                'openrouter_api_key': '',
                'llm_model': 'gpt-3.5-turbo',
                'llm_enabled': False,
                'email_notifications': False,
                'notification_email': '',
                'only_new_content': True,
                'min_relevance_score': 0.5,
                'request_delay_seconds': 1.0,
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        }
        
        # Create monitoring service
        monitoring_service = IncentivesMonitoringService(
            db_manager, 
            monitoring_config, 
            None  # No email service for testing
        )
        
        print("  ✅ Monitoring service created")
        
        # Test configuration
        print(f"  📋 Configuration:")
        print(f"    - Enabled: {monitoring_service.config.enabled}")
        print(f"    - Frequency: {monitoring_service.config.frequency}")
        print(f"    - Keywords: {monitoring_service.config.keywords}")
        print(f"    - Sources: {[s.value for s in monitoring_service.config.sources]}")
        print(f"    - LLM enabled: {monitoring_service.config.llm_enabled}")
        
        # Test a monitoring session (limited to avoid long execution)
        print("\n  🔄 Running test monitoring session...")
        print("     (This may take a few moments...)")
        
        session = monitoring_service.run_monitoring_session()
        
        print(f"  ✅ Session completed:")
        print(f"    - Status: {session.status}")
        print(f"    - Items found: {session.items_found}")
        print(f"    - New items: {session.new_items}")
        print(f"    - Success: {session.success}")
        
        if session.errors_encountered:
            print(f"    - Errors: {session.errors_encountered}")
        
        # Clean up
        db_manager.close()
        if os.path.exists(db_path):
            os.remove(db_path)
            
    except Exception as e:
        print(f"  ❌ Monitoring service error: {e}")
        import traceback
        traceback.print_exc()
    
    print()

def main():
    """Run all tests"""
    print("🚀 Italian Financial Incentives Monitoring System - Test Suite")
    print("=" * 70)
    print()
    
    # Run tests
    test_database_integration()
    test_web_scraping()
    test_llm_service()
    test_monitoring_service()
    
    print("=" * 70)
    print("✅ Test suite completed!")
    print()
    print("📋 Next Steps:")
    print("1. Add your OpenRouter API key to enable LLM analysis")
    print("2. Configure email settings for notifications")
    print("3. Set up monitoring schedule in the app settings")
    print("4. Run the main application to see the incentives interface")
    print()
    print("🎯 The incentives monitoring system is ready to use!")

if __name__ == "__main__":
    main()
