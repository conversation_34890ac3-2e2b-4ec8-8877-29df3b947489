#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Layout principale per Agevolami PM
"""

import flet as ft
from typing import Optional, Callable, Dict, Any
from datetime import datetime

from core import get_logger
from ui.components.sidebar import Sidebar
from ui.components.header import Header
from ui.components.alert_panel import AlertPanel
from ui.views.dashboard import DashboardView
from ui.views.clients import ClientsView
from ui.views.projects import ProjectsView
from ui.views.sals import SALsView
from ui.views.tasks import TasksView
from ui.views.deadlines import DeadlinesView
from ui.views.incentives_view import IncentivesView
from ui.views.enhanced_calendar import EnhancedCalendarView
from ui.views.hybrid_calendar import HybridCalendarView
from ui.views.settings import SettingsView
from ui.views.gantt import GanttView
from ui.views.native_gantt import NativeGanttChart

logger = get_logger(__name__)

class MainLayout:
    """Layout principale dell'applicazione"""
    
    def __init__(self, page: ft.Page, app_instance):
        self.page = page
        self.app = app_instance
        self.current_view = "dashboard"

        # Initialize background services
        self._init_background_services()

        # Initialize data cache
        from core.services.data_cache_manager import get_data_cache
        self.data_cache = get_data_cache(app_instance.db_manager)

        # Componenti principali
        self.sidebar = None
        self.header = None
        self.alert_panel = None
        self.content_area = None

        # Views
        self.views = {}

        # Stato
        self.is_alert_panel_visible = False

        # Navigation history
        self.navigation_history = ["dashboard"]
        self.current_detail_view = None  # For tracking detail views

        # Keyboard shortcuts
        self.shortcuts_help_visible = False
        self._init_keyboard_shortcuts()

        self._init_components()
        self._init_views()

    def _init_keyboard_shortcuts(self):
        """Initialize keyboard shortcuts system"""
        try:
            # Set up page keyboard event handler
            self.page.on_keyboard_event = self._handle_keyboard_event
            logger.info("Keyboard event handler initialized")
            
            # Shortcut definitions
            self.shortcuts = {
                # Navigation shortcuts
                "ctrl+1": {"action": "navigate", "target": "dashboard", "description": "Dashboard"},
                "ctrl+2": {"action": "navigate", "target": "clients", "description": "Clients"},
                "ctrl+3": {"action": "navigate", "target": "projects", "description": "Projects"},
                "ctrl+4": {"action": "navigate", "target": "sals", "description": "SAL"},
                "ctrl+5": {"action": "navigate", "target": "tasks", "description": "Tasks"},
                "ctrl+6": {"action": "navigate", "target": "deadlines", "description": "Deadlines"},
                "ctrl+7": {"action": "navigate", "target": "calendar", "description": "Calendar"},
                "ctrl+8": {"action": "navigate", "target": "gantt", "description": "Gantt Chart"},
                
                # Action shortcuts
                "ctrl+k": {"action": "search", "description": "Open Search"},
                "ctrl+n": {"action": "create_new", "description": "Create New (context-sensitive)"},
                "ctrl+s": {"action": "save", "description": "Save current form"},
                "ctrl+o": {"action": "open_settings", "description": "Open Settings"},
                "ctrl+u": {"action": "manual_update", "description": "Check for Updates"},
                "ctrl+e": {"action": "export_png", "description": "Export as PNG"},
                "ctrl+p": {"action": "export_pdf", "description": "Export as PDF"},
                "escape": {"action": "cancel", "description": "Cancel/Close"},
                "f1": {"action": "help", "description": "Show keyboard shortcuts"},
            }
            
            logger.info(f"Initialized {len(self.shortcuts)} keyboard shortcuts")
            
        except Exception as e:
            logger.error(f"Keyboard shortcuts initialization error: {e}")
            # Create empty shortcuts dict as fallback
            self.shortcuts = {}

    def _handle_keyboard_event(self, e: ft.KeyboardEvent):
        """Handle keyboard events for shortcuts"""
        try:
            # Build shortcut key
            key_combo = ""
            if e.ctrl:
                key_combo += "ctrl+"
            if e.alt:
                key_combo += "alt+"
            if e.shift:
                key_combo += "shift+"
            
            # Normalize key names
            key = e.key.lower()
            
            # Handle key variants
            if key in ["escape", "esc"]:
                key = "escape"
            elif key in ["f1"]:
                key = "f1"
            
            key_combo += key
            
            # Debug logging
            logger.info(f"Keyboard event: original='{e.key}' normalized='{key}' combo='{key_combo}'")
            
            # Handle shortcut if it exists
            if key_combo in self.shortcuts:
                logger.info(f"Executing shortcut: {key_combo}")
                self._execute_shortcut(self.shortcuts[key_combo])
                return
            
            # Try alternative key names for escape
            if key in ["escape", "esc"] and not e.ctrl and not e.alt and not e.shift:
                if "escape" in self.shortcuts:
                    logger.info(f"Executing escape shortcut")
                    self._execute_shortcut(self.shortcuts["escape"])
                    return
            
            logger.debug(f"No shortcut found for: {key_combo}")
                
        except Exception as ex:
            logger.error(f"Keyboard shortcut error: {ex}")

    def _execute_shortcut(self, shortcut: Dict[str, str]):
        """Execute a keyboard shortcut action"""
        action = shortcut["action"]
        
        try:
            logger.info(f"Executing action: {action}")
            
            if action == "navigate":
                target = shortcut["target"]
                self._navigate_to(target)
                
            elif action == "search":
                # Focus on search input
                if self.header and self.header.search_box:
                    search_input = self.header.search_box.search_input
                    search_input.focus()
                    self.page.update()
                    
            elif action == "create_new":
                self._handle_context_new()
                
            elif action == "save":
                self._handle_save_shortcut()
                
            elif action == "cancel":
                self._handle_cancel_shortcut()
                
            elif action == "help":
                self._toggle_shortcuts_help()
                
            elif action == "open_settings":
                self._navigate_to("settings")
                
            elif action == "manual_update":
                self._handle_manual_update()
                
            elif action == "export_png":
                self._handle_export_png()
                
            elif action == "export_pdf":
                self._handle_export_pdf()
                
        except Exception as e:
            logger.error(f"Shortcut execution error: {e}")

    def _handle_context_new(self):
        """Handle context-sensitive Ctrl+N shortcut"""
        current_view = self.current_view
        
        try:
            if current_view == "clients":
                view = self._get_view("clients")
                if view and hasattr(view, '_show_client_form'):
                    view._show_client_form()
                    
            elif current_view == "projects":
                view = self._get_view("projects")
                if view and hasattr(view, '_show_project_form'):
                    view._show_project_form()

            elif current_view == "sals":
                view = self._get_view("sals")
                if view and hasattr(view, '_show_sal_form'):
                    view._show_sal_form()

            elif current_view == "tasks":
                view = self._get_view("tasks")
                if view and hasattr(view, '_show_task_form'):
                    view._show_task_form()
                    
            elif current_view == "deadlines":
                view = self._get_view("deadlines")
                if view and hasattr(view, '_show_deadline_form'):
                    view._show_deadline_form()
                    
            elif current_view == "dashboard":
                # Show quick create menu
                self._show_quick_create_menu()
                
            else:
                logger.info(f"No create action defined for view: {current_view}")
                
        except Exception as e:
            logger.error(f"Context new error: {e}")

    def _handle_save_shortcut(self):
        """Handle Ctrl+S save shortcut"""
        logger.info(f"Save shortcut triggered in view: {self.current_view}")
        
        try:
            view = self._get_view(self.current_view)
            if view:
                # Look for common save methods
                save_methods = ['_save_current_form', 'save_current_form', 'save', '_handle_save', 'handle_save']
                
                for method_name in save_methods:
                    if hasattr(view, method_name):
                        method = getattr(view, method_name)
                        logger.info(f"Calling save method: {method_name}")
                        method()
                        # Show feedback
                        self._show_shortcut_feedback("Save triggered")
                        return
                
                # If no save method found, show feedback
                self._show_shortcut_feedback("No save action available")
                logger.info("No save method found for current view")
            else:
                logger.warning("No view found for save shortcut")
                
        except Exception as e:
            logger.error(f"Save shortcut error: {e}")
            self._show_shortcut_feedback(f"Save error: {str(e)}")

    def _handle_cancel_shortcut(self):
        """Handle Esc cancel shortcut"""
        logger.info(f"Cancel shortcut triggered in view: {self.current_view}")
        
        try:
            # Close shortcuts help if open
            if self.shortcuts_help_visible:
                self._toggle_shortcuts_help()
                self._show_shortcut_feedback("Help closed")
                return
                
            # Close alert panel if open
            if self.is_alert_panel_visible:
                self._toggle_alert_panel()
                self._show_shortcut_feedback("Alert panel closed")
                return
                
            # Try to find and trigger cancel on current view
            view = self._get_view(self.current_view)
            if view:
                cancel_methods = ['_cancel_current_form', 'cancel_current_form', 'cancel', '_handle_cancel', 'handle_cancel']
                
                for method_name in cancel_methods:
                    if hasattr(view, method_name):
                        method = getattr(view, method_name)
                        logger.info(f"Calling cancel method: {method_name}")
                        method()
                        self._show_shortcut_feedback("Cancel triggered")
                        return
                
                # If no cancel method found, show feedback
                self._show_shortcut_feedback("No cancel action available")
                logger.info("No cancel method found for current view")
            else:
                logger.warning("No view found for cancel shortcut")
                
        except Exception as e:
            logger.error(f"Cancel shortcut error: {e}")
            self._show_shortcut_feedback(f"Cancel error: {str(e)}")

    def _handle_export_png(self):
        """Handle Ctrl+E export PNG shortcut"""
        logger.info(f"Export PNG shortcut triggered in view: {self.current_view}")
        
        try:
            view = self._get_view(self.current_view)
            if view:
                # Look for export PNG methods
                export_methods = ['export_png', '_export_png', 'export_as_png', '_export_as_png']
                
                for method_name in export_methods:
                    if hasattr(view, method_name):
                        method = getattr(view, method_name)
                        logger.info(f"Calling export PNG method: {method_name}")
                        method()
                        return
                
                # If no export method found, log info
                logger.info("No PNG export method found for current view")
            else:
                logger.warning("No view found for export PNG shortcut")
                
        except Exception as e:
            logger.error(f"Export PNG shortcut error: {e}")

    def _handle_export_pdf(self):
        """Handle Ctrl+P export PDF shortcut"""
        logger.info(f"Export PDF shortcut triggered in view: {self.current_view}")
        
        try:
            view = self._get_view(self.current_view)
            if view:
                # Look for export PDF methods
                export_methods = ['export_pdf', '_export_pdf', 'export_as_pdf', '_export_as_pdf']
                
                for method_name in export_methods:
                    if hasattr(view, method_name):
                        method = getattr(view, method_name)
                        logger.info(f"Calling export PDF method: {method_name}")
                        method()
                        return
                
                # If no export method found, log info
                logger.info("No PDF export method found for current view")
            else:
                logger.warning("No view found for export PDF shortcut")
                
        except Exception as e:
            logger.error(f"Export PDF shortcut error: {e}")

    def _handle_manual_update(self):
        """Handle manual update trigger from keyboard shortcut"""
        logger.info("Manual update shortcut triggered")
        
        try:
            if hasattr(self.app, 'update_manager') and self.app.update_manager:
                # Show loading notification
                if hasattr(self.page, 'open'):
                    self.page.open(
                        ft.SnackBar(
                            content=ft.Text("🔄 Controllo aggiornamenti... (Ctrl+U)"),
                            duration=2000,
                            bgcolor=ft.Colors.BLUE_600
                        )
                    )
                
                # Trigger manual update check
                self.app.update_manager.check_for_updates_manual()
                logger.info("Manual update check initiated via keyboard shortcut")
            else:
                # Fallback: navigate to settings updates section
                self._navigate_to("settings")
                logger.info("Navigated to settings for manual update")
                    
        except Exception as e:
            logger.error(f"Manual update shortcut error: {e}")
            if hasattr(self.page, 'open'):
                self.page.open(
                    ft.SnackBar(
                        content=ft.Text(f"❌ Errore controllo aggiornamenti: {str(e)}"),
                        duration=3000,
                        bgcolor=ft.Colors.RED_600
                    )
                )

    def _show_shortcut_feedback(self, message: str):
        """Show brief feedback for shortcuts - disabled to reduce UI noise"""
        # Feedback disabled to provide cleaner user experience
        pass

    def _show_quick_create_menu(self):
        """Show quick create menu for dashboard"""
        def create_client(e):
            dialog.open = False
            self.page.update()
            self._navigate_to("clients")
            view = self._get_view("clients")
            if view and hasattr(view, '_show_client_form'):
                view._show_client_form()

        def create_project(e):
            dialog.open = False
            self.page.update()
            self._navigate_to("projects")
            view = self._get_view("projects")
            if view and hasattr(view, '_show_project_form'):
                view._show_project_form()

        def create_deadline(e):
            dialog.open = False
            self.page.update()
            self._navigate_to("deadlines")
            view = self._get_view("deadlines")
            if view and hasattr(view, '_show_deadline_form'):
                view._show_deadline_form()

        def close_menu(e):
            dialog.open = False
            self.page.update()

        dialog = ft.AlertDialog(
            title=ft.Text("Create New", weight=ft.FontWeight.BOLD),
            content=ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.PERSON, color=ft.Colors.BLUE_600),
                    title=ft.Text("New Client"),
                    on_click=create_client
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.WORK, color=ft.Colors.GREEN_600),
                    title=ft.Text("New Project"),
                    on_click=create_project
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.SCHEDULE, color=ft.Colors.ORANGE_600),
                    title=ft.Text("New Deadline"),
                    on_click=create_deadline
                ),
            ], tight=True),
            actions=[
                ft.TextButton("Cancel", on_click=close_menu)
            ]
        )

        self.page.overlay.append(dialog)
        dialog.open = True
        self.page.update()

    def _toggle_shortcuts_help(self):
        """Toggle keyboard shortcuts help dialog"""
        if self.shortcuts_help_visible:
            # Close help
            self.shortcuts_help_visible = False
            # Remove from overlay
            overlays_to_remove = [overlay for overlay in self.page.overlay if hasattr(overlay, 'title') and overlay.title and overlay.title.value == "Keyboard Shortcuts"]
            for overlay in overlays_to_remove:
                self.page.overlay.remove(overlay)
            self.page.update()
        else:
            # Show help
            self._show_shortcuts_help()

    def _show_shortcuts_help(self):
        """Show keyboard shortcuts help dialog"""
        def close_help(e):
            self.shortcuts_help_visible = False
            dialog.open = False
            self.page.update()

        # Group shortcuts by category
        navigation_shortcuts = []
        action_shortcuts = []
        
        for key, shortcut in self.shortcuts.items():
            display_key = key.replace("ctrl+", "Ctrl + ").replace("escape", "Esc").replace("f1", "F1").upper()
            
            if shortcut["action"] == "navigate":
                navigation_shortcuts.append(
                    ft.Container(
                        content=ft.Row([
                            ft.Container(
                                content=ft.Text(display_key, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                                width=100
                            ),
                            ft.Text(shortcut["description"], expand=True)
                        ]),
                        padding=ft.padding.symmetric(vertical=4)
                    )
                )
            else:
                action_shortcuts.append(
                    ft.Container(
                        content=ft.Row([
                            ft.Container(
                                content=ft.Text(display_key, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                                width=100
                            ),
                            ft.Text(shortcut["description"], expand=True)
                        ]),
                        padding=ft.padding.symmetric(vertical=4)
                    )
                )

        dialog = ft.AlertDialog(
            title=ft.Text("Keyboard Shortcuts", weight=ft.FontWeight.BOLD),
            content=ft.Container(
                content=ft.Column([
                    ft.Text("Navigation", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                    ft.Divider(),
                    *navigation_shortcuts,
                    ft.Container(height=10),
                    ft.Text("Actions", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                    ft.Divider(),
                    *action_shortcuts
                ], scroll=ft.ScrollMode.AUTO),
                width=400,
                height=400
            ),
            actions=[
                ft.TextButton("Close", on_click=close_help)
            ]
        )

        self.page.overlay.append(dialog)
        dialog.open = True
        self.shortcuts_help_visible = True
        self.page.update()

    def _init_background_services(self):
        """Initialize background services for better performance"""
        try:
            from core.services.background_service_manager import register_google_services
            register_google_services(self.app)
            logger.info("Background services initialized")
        except Exception as e:
            logger.error(f"Error initializing background services: {e}")

    def _init_components(self):
        """Inizializza i componenti del layout"""
        
        # Sidebar
        self.sidebar = Sidebar(
            on_menu_click=self._on_menu_click,
            on_alert_click=self._toggle_alert_panel
        )
        
        # Header
        self.header = Header(
            on_search=self._on_search,
            on_notification_click=self._toggle_alert_panel,
            on_settings_click=lambda: self._navigate_to("settings"),
            app_instance=self.app
        )
        
        # Alert Panel
        self.alert_panel = AlertPanel(
            app_instance=self.app,
            on_close=self._toggle_alert_panel
        )
        
        # Content Area
        self.content_area = ft.Container(
            content=ft.Text("Caricamento...", size=16),
            expand=True,
            padding=20,
            bgcolor=ft.Colors.GREY_50
        )
    
    def _init_views(self):
        """Inizializza le views dell'applicazione"""
        
        # Initialize only essential views immediately
        self.views = {
            "dashboard": DashboardView(self.app)
        }
        
        # Other views will be initialized lazily when first accessed
        self._view_classes = {
            "clients": ClientsView,
            "projects": ProjectsView,
            "sals": SALsView,
            "tasks": TasksView,
            "deadlines": DeadlinesView,
            "incentives": IncentivesView,
            "calendar": HybridCalendarView,  # Using new hybrid calendar by default
            "calendar_enhanced": EnhancedCalendarView,  # Keep enhanced as alternative
            "settings": SettingsView,
            "gantt": NativeGanttChart,  # Using ultra-fast Native Gantt 🚀
            "gantt_old": GanttView  # Keep old Gantt as fallback
        }
    
    def _on_menu_click(self, view_name: str):
        """Gestisce il click sui menu della sidebar"""
        self._navigate_to(view_name)
    
    def _navigate_to(self, view_name: str, add_to_history: bool = True):
        """Naviga a una view specifica"""
        if view_name == self.current_view:
            return
            
        # Add to navigation history if it's a main view
        if add_to_history and view_name in ["dashboard", "clients", "projects", "sals", "tasks", "deadlines", "incentives", "settings", "calendar", "gantt"]:
            if not self.navigation_history or self.navigation_history[-1] != view_name:
                self.navigation_history.append(view_name)
            # Keep history limited to last 10 items
            if len(self.navigation_history) > 10:
                self.navigation_history = self.navigation_history[-10:]
        
        self.current_view = view_name
        self.current_detail_view = None  # Reset detail view when navigating to main views
        
        # Aggiorna sidebar
        if self.sidebar:
            self.sidebar.set_active_menu(view_name)
        
        # Aggiorna header
        if self.header:
            view_titles = {
                "dashboard": "Dashboard",
                "clients": "Clienti",
                "projects": "Progetti",
                "sals": "SAL",
                "tasks": "Attività",
                "deadlines": "Scadenze",
                "incentives": "Incentivi",
                "settings": "Impostazioni",
                "calendar": "Calendario",
                "gantt": "Gantt Professionale"
            }
            self.header.set_title(view_titles.get(view_name, "Agevolami PM"))
            # Hide back button for main views
            self.header.set_back_button_visible(False)
        
        # Carica la view
        self._load_view(view_name)
        
        # Single page update at the end
        if self.page:
            self.page.update()
        
        logger.info(f"Navigazione a: {view_name}")
    
    def go_back(self):
        """Torna alla view precedente"""
        if self.current_detail_view:
            # If we're in a detail view, go back to the main view
            self.current_detail_view = None
            self._navigate_to(self.current_view, add_to_history=False)
            # Update header to hide back button
            if self.header:
                self.header.set_back_button_visible(False)
            return
        
        if len(self.navigation_history) > 1:
            # Remove current view from history
            self.navigation_history.pop()
            # Navigate to previous view
            previous_view = self.navigation_history[-1]
            self._navigate_to(previous_view, add_to_history=False)
        else:
            # If no history, go to dashboard
            self._navigate_to("dashboard", add_to_history=False)
    
    def navigate_to_detail(self, detail_type: str, detail_id: str = None):
        """Naviga a una vista dettaglio"""
        self.current_detail_view = {"type": detail_type, "id": detail_id}
        
        # Update header title for detail views
        if self.header:
            detail_titles = {
                "client_detail": "Dettagli Cliente",
                "project_detail": "Dettagli Progetto",
                "sal_detail": "Dettagli SAL",
                "deadline_detail": "Dettagli Scadenza"
            }
            title = detail_titles.get(detail_type, "Dettagli")
            self.header.set_title(title)
            # Show back button for detail views
            self.header.set_back_button_visible(True)
        
        # Load the detail view
        self._load_view(self.current_view)
        
        # Update page
        if self.page:
            self.page.update()
        
        logger.info(f"Navigazione a dettaglio: {detail_type} - {detail_id}")
    
    def _load_view(self, view_name: str):
        """Carica una view nel content area"""
        try:
            # Get the view (will be created lazily if needed)
            view = self._get_view(view_name)
            if not view:
                self.content_area.content = ft.Text(
                    f"View '{view_name}' non trovata",
                    size=16,
                    color=ft.Colors.RED
                )
                return
            
            # Check if we need to load a detail view
            if self.current_detail_view:
                detail_type = self.current_detail_view["type"]
                detail_id = self.current_detail_view.get("id")

                # For detail views, skip the refresh to avoid conflicts
                logger.info(f"Caricamento vista dettaglio: {detail_type}")

                # Load appropriate detail view
                if detail_type == "client_detail" and view_name == "clients":
                    if hasattr(view, 'show_client_detail'):
                        view.show_client_detail(detail_id)
                        return  # Exit early to avoid building the main view
                elif detail_type == "project_detail" and view_name == "projects":
                    if hasattr(view, 'show_project_detail'):
                        view.show_project_detail(detail_id)
                        return  # Exit early to avoid building the main view
                elif detail_type == "sal_detail" and view_name == "sals":
                    if hasattr(view, 'show_sal_detail'):
                        view.show_sal_detail(detail_id)
                        return  # Exit early to avoid building the main view
                # Add more detail views as needed
            else:
                # Force refresh dei dati solo se NON stiamo caricando una vista dettaglio
                if hasattr(view, 'refresh_data'):
                    view.refresh_data()
                elif hasattr(view, 'refresh'):
                    view.refresh()
            
            # Costruisce la view se necessario
            if hasattr(view, 'build'):
                view_content = view.build()
            else:
                view_content = view.get_content() if hasattr(view, 'get_content') else ft.Text("View non disponibile")
            
            # Aggiorna il content area (senza chiamare update)
            self.content_area.content = ft.Column([view_content], scroll=ft.ScrollMode.AUTO)
                    
        except Exception as e:
            logger.error(f"Errore caricamento view {view_name}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            self.content_area.content = ft.Column([
                ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED, size=48),
                ft.Text(
                    f"Errore caricamento view: {str(e)}",
                    size=16,
                    color=ft.Colors.RED,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.ElevatedButton(
                    text="Riprova",
                    on_click=lambda _: self._load_view(view_name),
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE
                )
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16)
    
    def _toggle_alert_panel(self):
        """Mostra/nasconde il pannello degli alert"""
        self.is_alert_panel_visible = not self.is_alert_panel_visible
        
        if self.is_alert_panel_visible:
            # Refresh degli alert
            self.alert_panel.refresh_alerts()
        
        self._update_layout()
    
    def _on_search(self, query: str):
        """Handle global search - now handled by header component"""
        # The search is now handled directly by the header component
        # This method is kept for compatibility but the actual search
        # logic is in the header's professional search engine
        logger.info(f"Global search triggered: {query}")
        pass
    
    def _update_layout(self):
        """Aggiorna il layout principale"""
        # Contenuto principale
        main_content = ft.Row([
            # Sidebar
            self.sidebar.build(),
            
            # Area principale
            ft.Column([
                # Header
                self.header.build(),
                
                # Content area
                self.content_area
            ], expand=True, spacing=0)
        ], spacing=0, expand=True)
        
        # Se il pannello alert è visibile, aggiungi overlay
        if self.is_alert_panel_visible:
            layout_content = ft.Stack([
                main_content,
                
                # Overlay alert panel
                ft.Container(
                    content=self.alert_panel.build(),
                    right=0,
                    top=0,
                    width=400,
                    height=self.page.height,
                    shadow=ft.BoxShadow(
                        spread_radius=1,
                        blur_radius=10,
                        color=ft.Colors.BLACK26
                    )
                )
            ], 
            # CRITICAL: Allow overlays to extend beyond bounds
            clip_behavior=ft.ClipBehavior.NONE)
        else:
            layout_content = main_content
        
        # Add search overlay to page overlay instead of main layout
        search_overlay = self.header.search_box.get_results_view()
        if search_overlay.visible and search_overlay not in self.page.overlay:
            self.page.overlay.append(search_overlay)
        elif not search_overlay.visible and search_overlay in self.page.overlay:
            try:
                self.page.overlay.remove(search_overlay)
            except ValueError:
                pass  # Already removed
        
        # Update page content with proper handling
        self.page.controls.clear()
        self.page.add(layout_content)
        self.page.update()

    def build(self) -> ft.Control:
        """Costruisce il layout principale"""
        # Ensure dashboard data is loaded first
        if self.current_view == "dashboard" and "dashboard" in self.views:
            try:
                dashboard_view = self.views["dashboard"]
                if hasattr(dashboard_view, 'refresh_data'):
                    dashboard_view.refresh_data()
            except Exception as e:
                logger.error(f"Error refreshing dashboard data: {e}")
        
        # Carica la view iniziale
        self._load_view(self.current_view)
        
        # Contenuto principale
        main_content = ft.Row([
            # Sidebar
            self.sidebar.build(),
            
            # Area principale
            ft.Column([
                # Header
                self.header.build(),
                
                # Content area
                self.content_area
            ], expand=True, spacing=0)
        ], spacing=0, expand=True)
        
        # Se il pannello alert è visibile, aggiungi overlay
        if self.is_alert_panel_visible:
            layout_content = ft.Stack([
                main_content,
                
                # Overlay alert panel
                ft.Container(
                    content=self.alert_panel.build(),
                    right=0,
                    top=0,
                    width=400,
                    height=self.page.height,
                    shadow=ft.BoxShadow(
                        spread_radius=1,
                        blur_radius=10,
                        color=ft.Colors.BLACK26
                    )
                )
            ], 
            # CRITICAL: Allow overlays to extend beyond bounds
            clip_behavior=ft.ClipBehavior.NONE)
        else:
            layout_content = main_content
        
        return layout_content
    
    def refresh_current_view(self):
        """Ricarica i dati per la vista corrente"""
        if self.current_view:
            self._load_view(self.current_view)
            self.page.update()
    
    def show_notification(self, message: str, severity: str = "info"):
        """Mostra una notifica"""
        colors = {
            "info": ft.Colors.BLUE,
            "success": ft.Colors.GREEN,
            "warning": ft.Colors.ORANGE,
            "error": ft.Colors.RED
        }
        
        self.page.open(
            ft.SnackBar(
                content=ft.Text(message),
                bgcolor=colors.get(severity, ft.Colors.BLUE),
                duration=4000
            )
        )
    
    def show_dialog(self, dialog: ft.AlertDialog):
        """Mostra un dialog"""
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()
    
    def close_dialog(self):
        """Chiude il dialog corrente"""
        if self.page.dialog:
            self.page.dialog.open = False
            self.page.update()
    
    def get_current_view_name(self) -> str:
        """Restituisce il nome della view corrente"""
        return self.current_view
    
    def get_view(self, view_name: str):
        """Restituisce una view specifica"""
        return self.views.get(view_name)
    
    def update_alert_count(self, count: int):
        """Aggiorna il contatore degli alert"""
        if self.sidebar:
            self.sidebar.update_alert_count(count)
        
        if self.header:
            self.header.update_notification_count(count)
        
        self.page.update()

    def _get_view(self, view_name: str):
        """Get a view, creating it lazily if needed"""
        if view_name not in self.views:
            if view_name in self._view_classes:
                logger.info(f"Lazy loading view: {view_name}")
                self.views[view_name] = self._view_classes[view_name](self.app)
            else:
                logger.error(f"Unknown view: {view_name}")
                return None
        
        return self.views.get(view_name)

    def test_keyboard_shortcuts(self):
        """Test method to verify keyboard shortcuts are working"""
        try:
            logger.info("Testing keyboard shortcuts...")
            
            # Test save shortcut
            logger.info("Testing save shortcut...")
            self._handle_save_shortcut()
            
            # Test cancel shortcut  
            logger.info("Testing cancel shortcut...")
            self._handle_cancel_shortcut()
            
            # Show feedback
            self._show_shortcut_feedback("Keyboard shortcuts test completed - check logs")
            
        except Exception as e:
            logger.error(f"Keyboard shortcuts test error: {e}")
            self._show_shortcut_feedback(f"Test error: {str(e)}")