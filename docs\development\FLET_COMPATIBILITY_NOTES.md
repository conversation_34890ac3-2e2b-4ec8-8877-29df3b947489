# Flet Version Compatibility Notes

## Current Setup
- **Flet Version**: 0.27.0
- **Project**: Age<PERSON>lami PM v2

## Known Compatibility Issues & Fixes

### 1. FilterChip Component
**Issue**: `ft.FilterChip` is not available in Flet 0.27.0
**Error**: `module 'flet' has no attribute 'FilterChip'`
**Fix**: Use `ft.ElevatedButton` with conditional styling instead

#### Before (not working in 0.27.0):
```python
ft.FilterChip(
    label=ft.Text("Mese"),
    selected=self.view_mode == "month",
    on_click=lambda _: self._change_view_mode("month")
)
```

#### After (compatible with 0.27.0):
```python
ft.ElevatedButton(
    text="Mese",
    bgcolor=ft.Colors.BLUE_600 if self.view_mode == "month" else ft.Colors.GREY_300,
    color=ft.Colors.WHITE if self.view_mode == "month" else ft.Colors.GREY_700,
    on_click=lambda _: self._change_view_mode("month"),
    style=ft.ButtonStyle(
        shape=ft.RoundedRectangleBorder(radius=8),
        padding=ft.padding.symmetric(horizontal=16, vertical=8)
    )
)
```

## Components Available in Flet 0.27.0

### ✅ Available:
- `ft.ElevatedButton`
- `ft.OutlinedButton` 
- `ft.TextButton`
- `ft.IconButton`
- `ft.DatePicker`
- `ft.Container`
- `ft.Row`, `ft.Column`
- `ft.Text`
- `ft.Icon`
- `ft.Divider`
- `ft.Dropdown`

### ❌ Not Available (added in later versions):
- `ft.FilterChip`
- `ft.Chip`
- `ft.SegmentedButton`
- Some newer styling options

## Upgrade Considerations

If upgrading Flet to a newer version:
1. Update `requirements.txt`: `flet>=0.28.0` or later
2. Test all custom components
3. Can use modern components like `FilterChip`
4. Better performance and new features

## Current Status
✅ **Hybrid Calendar is fully compatible with Flet 0.27.0**
- All components work correctly
- Test script runs without errors
- Main app integration successful 