#!/usr/bin/env python3
"""
Test script to verify critical fixes for:
1. Settings persistence
2. Google authentication persistence  
3. Windows notifications
"""

import os
import sys
import json
import time
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

def test_settings_persistence():
    """Test that settings are properly saved and loaded"""
    print("🔧 Testing Settings Persistence...")
    
    try:
        from core.config.app_config import AppConfig

        # Test basic settings file persistence
        config = AppConfig()
        settings_file = config.data_dir / 'settings.json'

        # Test that we can read and write settings
        if settings_file.exists():
            import json
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings_data = json.load(f)

            # Test modifying and saving back
            test_key = 'test_persistence_key'
            test_value = 'test_persistence_value'

            # Add test data
            if 'email' not in settings_data:
                settings_data['email'] = {}
            settings_data['email'][test_key] = test_value

            # Save back
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, indent=2, ensure_ascii=False)

            # Read again to verify
            with open(settings_file, 'r', encoding='utf-8') as f:
                reloaded_data = json.load(f)

            persistence_test = reloaded_data.get('email', {}).get(test_key) == test_value

            # Clean up test data
            if test_key in reloaded_data.get('email', {}):
                del reloaded_data['email'][test_key]
                with open(settings_file, 'w', encoding='utf-8') as f:
                    json.dump(reloaded_data, f, indent=2, ensure_ascii=False)

            print(f"   ✅ Settings persistence: {'SUCCESS' if persistence_test else 'FAILED'}")
            return persistence_test
        else:
            print(f"   ⚠️  Settings file does not exist: {settings_file}")
            return False
        
    except Exception as e:
        print(f"   ❌ Settings test failed: {e}")
        return False

def test_google_auth_paths():
    """Test Google authentication token paths are consistent"""
    print("🔐 Testing Google Authentication Paths...")
    
    try:
        from core.config.app_config import AppConfig
        from core.services.google_tasks_service import GoogleTasksService
        from core.services.google_calendar_service import GoogleCalendarService
        from core.services.google_drive_service import GoogleDriveService
        
        config = AppConfig()
        config_dir = str(config.data_dir)
        
        # Initialize services
        tasks_service = GoogleTasksService(config_dir)
        calendar_service = GoogleCalendarService(config_dir)
        drive_service = GoogleDriveService(Path(config_dir))
        
        # Check that all services use the same config directory structure
        expected_config_dir = os.path.join(config_dir, "config")
        
        tasks_path_correct = tasks_service.config_dir == expected_config_dir
        calendar_path_correct = calendar_service.config_dir == expected_config_dir
        drive_path_correct = str(drive_service.config_dir) == expected_config_dir
        
        print(f"   ✅ Tasks config path: {'CORRECT' if tasks_path_correct else 'INCORRECT'}")
        print(f"      Expected: {expected_config_dir}")
        print(f"      Actual: {tasks_service.config_dir}")
        
        print(f"   ✅ Calendar config path: {'CORRECT' if calendar_path_correct else 'INCORRECT'}")
        print(f"      Expected: {expected_config_dir}")
        print(f"      Actual: {calendar_service.config_dir}")
        
        print(f"   ✅ Drive config path: {'CORRECT' if drive_path_correct else 'INCORRECT'}")
        print(f"      Expected: {expected_config_dir}")
        print(f"      Actual: {drive_service.config_dir}")
        
        return tasks_path_correct and calendar_path_correct and drive_path_correct
        
    except Exception as e:
        print(f"   ❌ Google auth paths test failed: {e}")
        return False

def test_windows_notifications():
    """Test Windows notifications are working"""
    print("🔔 Testing Windows Notifications...")
    
    try:
        from core.utils.windows_utils import WindowsNotificationManager
        
        # Initialize notification manager
        notification_manager = WindowsNotificationManager("Agevolami PM Test")
        
        # Test toast notification
        toast_result = notification_manager.show_toast(
            "Test Notification",
            "This is a test notification from Agevolami PM",
            duration=3,
            threaded=False
        )
        
        print(f"   ✅ Toast notification: {'SUCCESS' if toast_result else 'FAILED'}")
        
        # Test plyer notification as fallback
        plyer_result = notification_manager.show_notification_with_plyer(
            "Test Plyer Notification",
            "This is a test plyer notification",
            timeout=3
        )
        
        print(f"   ✅ Plyer notification: {'SUCCESS' if plyer_result else 'FAILED'}")
        
        return toast_result or plyer_result
        
    except Exception as e:
        print(f"   ❌ Windows notifications test failed: {e}")
        return False

def test_settings_file_integrity():
    """Test that settings.json file is not corrupted by multiple systems"""
    print("📄 Testing Settings File Integrity...")
    
    try:
        from core.config.app_config import AppConfig
        
        config = AppConfig()
        settings_file = config.data_dir / 'settings.json'
        
        if settings_file.exists():
            # Try to load and parse the settings file
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings_data = json.load(f)
            
            # Check for required sections
            required_sections = ['email', 'notifications', 'windows', 'google_services', 'reports', 'application']
            missing_sections = [section for section in required_sections if section not in settings_data]
            
            if missing_sections:
                print(f"   ⚠️  Missing sections: {missing_sections}")
            else:
                print(f"   ✅ All required sections present")
            
            print(f"   ✅ Settings file is valid JSON")
            print(f"   ✅ File location: {settings_file}")
            
            return len(missing_sections) == 0
        else:
            print(f"   ⚠️  Settings file does not exist: {settings_file}")
            return False
            
    except json.JSONDecodeError as e:
        print(f"   ❌ Settings file is corrupted JSON: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Settings file integrity test failed: {e}")
        return False

def main():
    """Run all critical tests"""
    print("🚀 Running Critical Fixes Verification Tests")
    print("=" * 60)
    
    tests = [
        ("Settings Persistence", test_settings_persistence),
        ("Google Auth Paths", test_google_auth_paths),
        ("Windows Notifications", test_windows_notifications),
        ("Settings File Integrity", test_settings_file_integrity),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        start_time = time.time()
        result = test_func()
        end_time = time.time()
        
        results.append((test_name, result))
        status = "✅ PASSED" if result else "❌ FAILED"
        duration = end_time - start_time
        
        print(f"   {status} ({duration:.2f}s)")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL CRITICAL FIXES VERIFIED!")
        return True
    else:
        print("⚠️  Some tests failed - issues may persist")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
