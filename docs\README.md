# 📚 Agevolami PM Documentation

Welcome to the comprehensive documentation for Agevolami PM. This directory contains all technical documentation, guides, and reference materials organized by category.

## 📁 Documentation Structure

### 🚀 [Features](./features/)
Documentation for new features, enhancements, and improvements:

- [Feature Roadmap](./features/FEATURE_ROADMAP.md) - Project roadmap and planned features
- [Flexible Task Form Summary](./features/FLEXIBLE_TASK_FORM_SUMMARY.md) - Enhanced task creation interface
- [Gantt Enhancements Summary](./features/GANTT_ENHANCEMENTS_SUMMARY.md) - Gantt chart improvements
- [Native Gantt Enhancements](./features/NATIVE_GANTT_ENHANCEMENTS.md) - Native Gantt implementation
- [Pro Gantt README](./features/PRO_GANTT_README.md) - Professional Gantt features
- [Hybrid Calendar README](./features/HYBRID_CALENDAR_README.md) - Calendar integration features
- [Search Improvements](./features/SEARCH_IMPROVEMENTS.md) - Enhanced search functionality
- [PDF Branded Enhancements](./features/PDF_BRANDED_ENHANCEMENTS.md) - PDF export improvements
- [PDF Export Optimizations](./features/PDF_EXPORT_OPTIMIZATIONS.md) - PDF performance optimizations
- [Backup Improvements](./features/BACKUP_IMPROVEMENTS.md) - Backup system enhancements
- [Email Reporting and Settings Improvements](./features/EMAIL_REPORTING_AND_SETTINGS_IMPROVEMENTS.md) - Email features
- [Windows Features](./features/WINDOWS_FEATURES.md) - Windows-specific functionality

### 🔧 [Fixes](./fixes/)
Bug fixes, troubleshooting guides, and issue resolutions:

- [Critical Fixes Summary](./fixes/CRITICAL_FIXES_SUMMARY.md) - Overview of critical bug fixes
- [Duplicate Prevention Summary](./fixes/DUPLICATE_PREVENTION_SUMMARY.md) - Duplicate data prevention
- [Email Issue Diagnosis and Fix](./fixes/EMAIL_ISSUE_DIAGNOSIS_AND_FIX.md) - Email system fixes
- [Nuclear Grey Screen Fix](./fixes/NUCLEAR_GREY_SCREEN_FIX.md) - UI grey screen issues
- [Update Grey Screen Fix Summary](./fixes/UPDATE_GREY_SCREEN_FIX_SUMMARY.md) - Update-related UI fixes
- [Google Authentication Enterprise Fix](./fixes/GOOGLE_AUTHENTICATION_ENTERPRISE_FIX.md) - Google auth fixes
- [Google Auth Enterprise Fix](./fixes/GOOGLE_AUTH_ENTERPRISE_FIX.md) - Additional Google auth fixes
- [Google Tasks Fixes](./fixes/GOOGLE_TASKS_FIXES.md) - Google Tasks integration fixes

### ⚙️ [Setup](./setup/)
Installation, configuration, and setup guides:

- [Auto Update Setup](./setup/AUTO_UPDATE_SETUP.md) - Automatic update configuration
- [Google Drive Setup](./setup/GOOGLE_DRIVE_SETUP.md) - Google Drive integration setup
- [GitHub Actions Troubleshooting](./setup/GITHUB_ACTIONS_TROUBLESHOOTING.md) - CI/CD troubleshooting
- [Settings Migration Plan](./setup/SETTINGS_MIGRATION_PLAN.md) - Settings system migration
- [Settings Transformation Complete](./setup/SETTINGS_TRANSFORMATION_COMPLETE.md) - Settings system overhaul

### 💻 [Development](./development/)
Technical documentation for developers:

- [Flet Compatibility Notes](./development/FLET_COMPATIBILITY_NOTES.md) - Framework compatibility information

### 🔗 [Integrations](./integrations/)
Third-party service integrations:

- [Google Calendar Improvements](./integrations/GOOGLE_CALENDAR_IMPROVEMENTS.md) - Google Calendar integration

### 🧪 [Testing](./testing/)
Testing documentation and guides:

- [Test UI Dialogs](./testing/test_ui_dialogs.md) - UI testing documentation

### 🕷️ Web Scraping & Technical
Technical documentation for advanced features:

- [Enhanced Web Scraping](./enhanced_web_scraping.md) - Web scraping service documentation
- [Grey Screen Fix](./grey-screen-fix.md) - UI issue resolution
- [Web Scraping 403 Analysis](./web_scraping_403_analysis.md) - Web scraping troubleshooting

## 🔍 Quick Navigation

### By Topic
- **UI/UX Issues**: [fixes/](./fixes/) - Grey screen fixes, UI improvements
- **Google Services**: [integrations/](./integrations/), [fixes/](./fixes/) - Google integration docs
- **Project Management**: [features/](./features/) - Gantt, tasks, calendar features
- **System Setup**: [setup/](./setup/) - Installation and configuration
- **Development**: [development/](./development/) - Technical development docs

### By Priority
- **🔴 Critical**: Start with [fixes/CRITICAL_FIXES_SUMMARY.md](./fixes/CRITICAL_FIXES_SUMMARY.md)
- **🟡 Setup**: See [setup/](./setup/) for installation guides
- **🟢 Features**: Explore [features/](./features/) for new functionality

## 📝 Contributing to Documentation

When adding new documentation:
1. Place files in the appropriate category folder
2. Update this README.md index
3. Use clear, descriptive filenames
4. Include proper markdown formatting

## 🏠 Back to Project

Return to the main project: [../README.md](../README.md)
