#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vista Tasks per Agevolami PM
"""

import os
import flet as ft
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from uuid import UUID, uuid4
import tempfile
from pathlib import Path

from core import get_logger
from core.models.base_models import Task, TaskStatus, Priority, Deadline, Project
from core.services import GoogleTasksService, EmailService

# PDF generation imports
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

logger = get_logger(__name__)

class TasksView:
    """Vista per la gestione delle task"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db_manager = app_instance.db_manager

        # Use background service manager for Google services
        from core.services.background_service_manager import get_google_tasks_service
        self._get_google_tasks_service = get_google_tasks_service
        
        self.current_tasks: List[Task] = []
        self.current_deadlines: Dict[UUID, Deadline] = {}
        self.current_projects: Dict[UUID, Project] = {}
        self.selected_task_id: Optional[UUID] = None
        self.filter_status = "all"  # all, pending, in_progress, completed
        self.filter_priority = "all"  # all, low, medium, high, critical
        self.search_query = ""
        self.hide_completed = True  # Hide completed tasks by default
        
        # Google Tasks synchronization tracking
        self.google_task_lists_cache: Dict[UUID, str] = {}  # deadline_id -> google_list_id
        self.synced_tasks_cache: Dict[UUID, str] = {}  # task_id -> google_task_id
        self.last_sync_timestamp: Optional[datetime] = None
        
        # Task form fields
        self.title_field = ft.TextField(
            label="Titolo Attività *",
            hint_text="Inserisci il titolo dell'attività",
            width=400
        )
        
    @property
    def google_tasks_service(self):
        """Get Google Tasks service from background service manager"""
        service = self._get_google_tasks_service()
        if service is None:
            # Return a dummy service that reports as disabled
            class DummyService:
                def is_enabled(self):
                    return False
                def is_authenticated(self):
                    return False
                def get_authentication_status(self):
                    return {"enabled": False, "authenticated": False}
            return DummyService()
        return service
    
    @property
    def google_tasks_settings(self) -> Dict[str, Any]:
        """Get Google Tasks settings from the settings view"""
        try:
            # Get settings directly from the settings view
            if hasattr(self.app, 'settings_view') and hasattr(self.app.settings_view, 'settings'):
                google_services = self.app.settings_view.settings.get('google_services', {})
                # Ensure all required keys exist with defaults
                return {
                    'tasks_enabled': google_services.get('tasks_enabled', False),
                    'tasks_auto_sync': google_services.get('tasks_auto_sync', True),
                    'tasks_sync_on_deadline_change': google_services.get('tasks_sync_on_deadline_change', True),
                    'tasks_create_list_per_deadline': google_services.get('tasks_create_list_per_deadline', True),
                    'tasks_sync_completed': google_services.get('tasks_sync_completed', True),
                    'tasks_authenticated': google_services.get('tasks_authenticated', False)
                }
        except Exception as e:
            logger.warning(f"Error getting Google Tasks settings: {e}")

        # Default fallback
        return {
            'tasks_enabled': False,
            'tasks_auto_sync': True,
            'tasks_sync_on_deadline_change': True,
            'tasks_create_list_per_deadline': True,
            'tasks_sync_completed': True,
            'tasks_authenticated': False
        }
    
    def _is_google_sync_enabled(self) -> bool:
        """Check if Google Tasks sync is enabled and authenticated"""
        try:
            # First check if the Google Tasks service itself is enabled
            service_enabled = self.google_tasks_service.is_enabled()

            # Get settings from the settings view
            settings = self.google_tasks_settings
            settings_enabled = settings.get('tasks_enabled', False)
            settings_authenticated = settings.get('tasks_authenticated', False)

            # Debug logging
            logger.debug(f"Google Tasks sync check - Service enabled: {service_enabled}, "
                        f"Settings enabled: {settings_enabled}, Settings authenticated: {settings_authenticated}")

            # If settings view is not available or not properly initialized,
            # fall back to checking the service directly
            if not hasattr(self.app, 'settings_view'):
                logger.debug("Settings view not available, checking service directly")
                return service_enabled

            # Use both service and settings checks
            result = (settings_enabled and settings_authenticated and service_enabled)

            # If settings say not enabled but service is enabled, refresh settings
            if not result and service_enabled:
                logger.info("Settings out of sync with service, refreshing...")
                self._refresh_google_tasks_status()
                # Re-check after refresh
                settings = self.google_tasks_settings
                result = (settings.get('tasks_enabled', False) and
                         settings.get('tasks_authenticated', False) and
                         service_enabled)

            return result

        except Exception as e:
            logger.error(f"Error checking Google Tasks sync status: {e}")
            # Fallback to service check only
            return self.google_tasks_service.is_enabled()
    
    def _should_auto_sync(self) -> bool:
        """Check if auto-sync is enabled"""
        settings = self.google_tasks_settings
        return (self._is_google_sync_enabled() and 
                settings.get('tasks_auto_sync', True))
    
    def _create_header(self) -> ft.Container:
        """Crea l'header della vista"""
        return ft.Container(
            content=ft.Row([
                ft.Text(
                    "Gestione Attività",
                    size=18,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                ft.Text(
                    f"({len(self.current_tasks)} totali)",
                    size=12,
                    color=ft.Colors.GREY_500
                ),

                ft.Container(expand=True),

                ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.SYNC,
                        tooltip="Sincronizza con Google Tasks",
                        on_click=self._sync_google_tasks,
                        icon_size=18,
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.TEAL_50,
                            color=ft.Colors.TEAL_600,
                            padding=ft.padding.all(8)
                        )
                    ),
                    ft.ElevatedButton(
                        text="Esporta",
                        icon=ft.Icons.DOWNLOAD,
                        height=32,
                        bgcolor=ft.Colors.GREEN_600,
                        color=ft.Colors.WHITE,
                        on_click=lambda _: self._show_export_dialog()
                    ),
                    ft.ElevatedButton(
                        text="Nuova Attività",
                        icon=ft.Icons.ADD_TASK,
                        height=32,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        on_click=self._show_task_form
                    )
                ], spacing=8)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.only(bottom=12)
        )
    
    def _create_filters(self) -> ft.Container:
        """Crea i filtri per le task"""
        return ft.Container(
            content=ft.Row([
                ft.TextField(
                    hint_text="Cerca attività...",
                    prefix_icon=ft.Icons.SEARCH,
                    width=250,
                    value=self.search_query,
                    on_change=self._on_search_change
                ),

                ft.Dropdown(
                    label="Stato",
                    width=120,
                    value=self.filter_status,
                    options=[
                        ft.dropdown.Option(text="Tutti", key="all"),
                        ft.dropdown.Option(text="In Attesa", key="in_attesa"),
                        ft.dropdown.Option(text="In Corso", key="in_corso"),
                        ft.dropdown.Option(text="In Pausa", key="in_pausa"),
                        ft.dropdown.Option(text="Completato", key="completato"),
                        ft.dropdown.Option(text="Annullato", key="annullato")
                    ],
                    on_change=self._on_status_filter_change
                ),

                ft.Dropdown(
                    label="Priorità",
                    width=120,
                    value=self.filter_priority,
                    options=[
                        ft.dropdown.Option(text="Tutte", key="all"),
                        ft.dropdown.Option(text="Bassa", key="bassa"),
                        ft.dropdown.Option(text="Media", key="media"),
                        ft.dropdown.Option(text="Alta", key="alta"),
                        ft.dropdown.Option(text="Critica", key="critica")
                    ],
                    on_change=self._on_priority_filter_change
                ),

                # Compact toggle button for hiding completed tasks
                ft.Container(
                    content=ft.Row([
                        ft.Icon(
                            ft.Icons.VISIBILITY_OFF if self.hide_completed else ft.Icons.VISIBILITY,
                            size=14,
                            color=ft.Colors.BLUE_600 if self.hide_completed else ft.Colors.GREY_600
                        ),
                        ft.Text(
                            "Nascondi completate" if self.hide_completed else "Mostra completate",
                            size=10,
                            color=ft.Colors.BLUE_600 if self.hide_completed else ft.Colors.GREY_600
                        )
                    ], spacing=4),
                    padding=ft.padding.symmetric(horizontal=8, vertical=4),
                    border_radius=4,
                    border=ft.border.all(1, ft.Colors.BLUE_600 if self.hide_completed else ft.Colors.GREY_400),
                    bgcolor=ft.Colors.BLUE_50 if self.hide_completed else ft.Colors.TRANSPARENT,
                    on_click=self._toggle_hide_completed,
                    tooltip="Mostra/nascondi completate"
                ),

                ft.Container(expand=True),

                ft.IconButton(
                    icon=ft.Icons.REFRESH,
                    tooltip="Aggiorna",
                    icon_size=18,
                    on_click=lambda _: self.refresh_data()
                )
            ], spacing=8),
            padding=ft.padding.only(bottom=10),
            bgcolor=ft.Colors.WHITE,
            border_radius=6,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_task_list(self) -> ft.Container:
        """Crea la lista delle task"""
        if not self.current_tasks:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.TASK_ALT, size=64, color=ft.Colors.GREY_400),
                    ft.Text("Nessuna attività trovata", size=16, color=ft.Colors.GREY_600),
                    ft.Text("Crea la tua prima attività per iniziare", size=12, color=ft.Colors.GREY_400),
                    ft.ElevatedButton(
                        text="Crea Attività",
                        icon=ft.Icons.ADD,
                        on_click=self._show_task_form,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center,
                expand=True
            )
        
        # Filter tasks
        filtered_tasks = self._filter_tasks()
        
        task_cards = []
        for task in filtered_tasks:
            task_cards.append(self._create_task_card(task))
        
        return ft.Container(
            content=ft.Column(
                task_cards,
                spacing=8,
                scroll=ft.ScrollMode.AUTO
            ),
            expand=True
        )
    
    def _create_task_card(self, task: Task) -> ft.Container:
        """Crea una card per una task"""
        # Get associated deadline and project
        deadline = self.current_deadlines.get(task.deadline_id)
        project = self.current_projects.get(task.project_id) if deadline else None
        
        # Status colors
        status_colors = {
            TaskStatus.PENDING: ft.Colors.ORANGE_100,
            TaskStatus.IN_PROGRESS: ft.Colors.BLUE_100,
            TaskStatus.WAITING: ft.Colors.YELLOW_100,
            TaskStatus.COMPLETED: ft.Colors.GREEN_100,
            TaskStatus.CANCELLED: ft.Colors.RED_100
        }
        
        # Priority colors
        priority_colors = {
            Priority.LOW: ft.Colors.GREEN,
            Priority.MEDIUM: ft.Colors.BLUE,
            Priority.HIGH: ft.Colors.ORANGE,
            Priority.CRITICAL: ft.Colors.RED
        }
        
        # Status text
        status_text = {
            TaskStatus.PENDING: "In Attesa",
            TaskStatus.IN_PROGRESS: "In Corso",
            TaskStatus.WAITING: "In Pausa",
            TaskStatus.COMPLETED: "Completato",
            TaskStatus.CANCELLED: "Annullato"
        }
        
        is_selected = self.selected_task_id == task.id
        
        return ft.Container(
            content=ft.Column([
                # Header row
                ft.Row([
                    ft.Row([
                        # Subtask indicator
                        ft.Icon(
                            ft.Icons.SUBDIRECTORY_ARROW_RIGHT,
                            size=16,
                            color=ft.Colors.GREY_600
                        ) if task.parent_task_id else ft.Container(width=0),
                        
                        ft.Container(
                            content=ft.Icon(
                                ft.Icons.CIRCLE,
                                size=8,
                                color=priority_colors.get(task.priority if hasattr(task.priority, 'value') else Priority(str(task.priority)), ft.Colors.GREY)
                            ),
                            tooltip=f"Priorità: {(task.priority.value if hasattr(task.priority, 'value') else str(task.priority)).title()}"
                        ),
                        ft.Text(
                            task.title,
                            size=14,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800,
                            expand=True
                        ),
                        
                        # Subtask count indicator
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.LIST, size=12, color=ft.Colors.BLUE_600),
                                ft.Text(
                                    str(len(self.db_manager.get_subtasks(task.id))),
                                    size=10,
                                    color=ft.Colors.BLUE_600,
                                    weight=ft.FontWeight.BOLD
                                )
                            ], spacing=2),
                            bgcolor=ft.Colors.BLUE_50,
                            border_radius=6,
                            padding=ft.padding.symmetric(horizontal=4, vertical=2),
                            tooltip=f"{len(self.db_manager.get_subtasks(task.id))} sottotask"
                        ) if len(self.db_manager.get_subtasks(task.id)) > 0 else ft.Container(width=0)
                    ], spacing=8, expand=True),
                    
                    ft.Container(
                        content=ft.Text(
                            status_text.get(task.status if hasattr(task.status, 'value') else TaskStatus(str(task.status)), (task.status.value if hasattr(task.status, 'value') else str(task.status)).title()),
                            size=11,
                            color=ft.Colors.GREY_700,
                            weight=ft.FontWeight.W_500
                        ),
                        bgcolor=status_colors.get(task.status if hasattr(task.status, 'value') else TaskStatus(str(task.status)), ft.Colors.GREY_100),
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        border_radius=12
                    ),
                    
                    ft.PopupMenuButton(
                        icon=ft.Icons.MORE_VERT,
                        items=[
                            ft.PopupMenuItem(
                                text="Modifica",
                                icon=ft.Icons.EDIT,
                                on_click=lambda _: self._edit_task(task)
                            ),
                            ft.PopupMenuItem(
                                text="Aggiungi Sottotask",
                                icon=ft.Icons.ADD_TASK,
                                on_click=lambda _: self._create_subtask(task)
                            ),
                            ft.PopupMenuItem(
                                text="Completa" if (task.status.value if hasattr(task.status, 'value') else str(task.status)) != "completato" else "Riapri",
                                icon=ft.Icons.CHECK_CIRCLE if (task.status.value if hasattr(task.status, 'value') else str(task.status)) != "completato" else ft.Icons.REPLAY,
                                on_click=lambda _: self._toggle_task_completion(task)
                            ),
                            ft.PopupMenuItem(),  # Divider
                            ft.PopupMenuItem(
                                text="Elimina",
                                icon=ft.Icons.DELETE,
                                on_click=lambda _: self._delete_task(task)
                            )
                        ]
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                # Description
                ft.Text(
                    task.description or "Nessuna descrizione",
                    size=12,
                    color=ft.Colors.GREY_600,
                    max_lines=2
                ) if task.description else ft.Container(),
                
                # Progress bar
                ft.Container(
                    content=ft.ProgressBar(
                        value=task.progress_percentage / 100,
                        color=ft.Colors.BLUE,
                        bgcolor=ft.Colors.GREY_200
                    ),
                    width=None
                ) if task.progress_percentage > 0 else ft.Container(),
                
                # Details row
                ft.Row([
                    ft.Row([
                        ft.Icon(
                            ft.Icons.SCHEDULE if deadline else ft.Icons.WORK, 
                            size=14, 
                            color=ft.Colors.GREY_500
                        ),
                        ft.Text(
                            deadline.title if deadline else f"Progetto: {project.name}" if project else "Attività standalone",
                            size=11,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4),
                    
                    ft.Row([
                        ft.Icon(ft.Icons.WORK, size=14, color=ft.Colors.GREY_500),
                        ft.Text(
                            project.name if project else "Nessun progetto",
                            size=11,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4) if project else ft.Container(),
                    
                    ft.Container(expand=True),
                    
                    ft.Row([
                        ft.Icon(ft.Icons.ACCESS_TIME, size=14, color=ft.Colors.GREY_500),
                        ft.Text(
                            f"{task.estimated_hours}h" if task.estimated_hours else "N/A",
                            size=11,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4) if task.estimated_hours else ft.Container(),
                    
                    ft.Text(
                        task.due_date.strftime("%d/%m %H:%M") if task.due_date and hasattr(task.due_date, 'hour') else (task.due_date.strftime("%d/%m") if task.due_date else ""),
                        size=11,
                        color=ft.Colors.ORANGE_600 if task.due_date and (task.due_date.date() if hasattr(task.due_date, 'date') else task.due_date) < date.today() else ft.Colors.GREY_600,
                        weight=ft.FontWeight.BOLD if task.due_date and (task.due_date.date() if hasattr(task.due_date, 'date') else task.due_date) < date.today() else ft.FontWeight.NORMAL
                    ) if task.due_date else ft.Container()
                ], spacing=12, alignment=ft.MainAxisAlignment.START)
            ], spacing=6),
            padding=ft.padding.all(10),
            margin=ft.margin.only(bottom=4),
            bgcolor=ft.Colors.BLUE_50 if is_selected else ft.Colors.WHITE,
            border_radius=6,
            border=ft.border.all(
                2 if is_selected else 1,
                ft.Colors.BLUE_400 if is_selected else ft.Colors.GREY_200
            ),
            on_click=lambda _: self._select_task(task.id)
        )
    
    def _filter_tasks(self) -> List[Task]:
        """Filtra le task in base ai criteri selezionati e organizza gerarchicamente"""
        filtered = self.current_tasks

        # Filter by completion status (hide completed if enabled)
        if self.hide_completed:
            filtered = [t for t in filtered if
                       (t.status.value if hasattr(t.status, 'value') else str(t.status)) != "completato"]

        # Filter by status
        if self.filter_status != "all":
            filtered = [t for t in filtered if
                       (t.status.value if hasattr(t.status, 'value') else str(t.status)) == self.filter_status]

        # Filter by priority
        if self.filter_priority != "all":
            filtered = [t for t in filtered if
                       (t.priority.value if hasattr(t.priority, 'value') else str(t.priority)) == self.filter_priority]

        # Filter by search query
        if self.search_query:
            query = self.search_query.lower()
            filtered = [t for t in filtered if
                       query in t.title.lower() or
                       (t.description and query in t.description.lower())]
        
        # Organize hierarchically: show parent tasks first, then their subtasks
        organized_tasks = []
        processed_tasks = set()
        
        # Sort by due date, then by priority
        def sort_key(task):
            priority_order = {"critica": 0, "alta": 1, "media": 2, "bassa": 3}
            # Handle both datetime and date objects, convert to date for comparison
            if task.due_date:
                due_date = task.due_date.date() if hasattr(task.due_date, 'date') else task.due_date
            else:
                due_date = date.max
            # Handle both enum and string values for priority
            priority_value = task.priority.value if hasattr(task.priority, 'value') else str(task.priority)
            priority = priority_order.get(priority_value, 4)
            return (due_date, priority)
        
        # First, add all parent tasks (tasks without parent_task_id)
        parent_tasks = [t for t in filtered if not t.parent_task_id]
        parent_tasks = sorted(parent_tasks, key=sort_key)
        
        for parent in parent_tasks:
            if parent.id not in processed_tasks:
                organized_tasks.append(parent)
                processed_tasks.add(parent.id)
                
                # Add subtasks immediately after their parent
                subtasks = [t for t in filtered if t.parent_task_id == parent.id]
                subtasks = sorted(subtasks, key=sort_key)
                for subtask in subtasks:
                    if subtask.id not in processed_tasks:
                        organized_tasks.append(subtask)
                        processed_tasks.add(subtask.id)
        
        # Add any remaining tasks (orphaned subtasks)
        remaining_tasks = [t for t in filtered if t.id not in processed_tasks]
        remaining_tasks = sorted(remaining_tasks, key=sort_key)
        organized_tasks.extend(remaining_tasks)
        
        return organized_tasks
    
    def _on_search_change(self, e):
        """Handle search query change"""
        self.search_query = e.control.value
        self._refresh_task_list()
    
    def _on_status_filter_change(self, e):
        """Handle status filter change"""
        self.filter_status = e.control.value
        self._refresh_task_list()
    
    def _on_priority_filter_change(self, e):
        """Handle priority filter change"""
        self.filter_priority = e.control.value
        self._refresh_task_list()

    def _toggle_hide_completed(self, e):
        """Toggle hiding completed tasks"""
        self.hide_completed = not self.hide_completed
        self._refresh_task_list()

    def _refresh_task_list(self):
        """Refresh the task list"""
        # Rebuild the entire view content
        if hasattr(self, 'app') and hasattr(self.app, 'page') and self.app.page:
            # Update the main layout's content area
            if hasattr(self.app, 'main_layout') and hasattr(self.app.main_layout, 'content_area'):
                new_content = self.build()
                self.app.main_layout.content_area.content = new_content
            
            # Trigger page update
            self.app.page.update()
    
    def _select_task(self, task_id: UUID):
        """Select a task"""
        self.selected_task_id = task_id
        self._refresh_task_list()
    
    def _show_task_form(self, e=None, task: Task = None, subtask_data=None):
        """Show form to create/edit task with modern UI"""
        is_edit = task is not None
        is_subtask = subtask_data is not None
        
        if is_subtask:
            title = "Nuova Sottotask"
        else:
            title = "Modifica Attività" if is_edit else "Nuova Attività"

        # Stile comune per i campi
        field_style = {
            "bgcolor": ft.Colors.WHITE,
            "border_color": ft.Colors.GREY_300,
            "border_radius": 8,
            "content_padding": ft.padding.symmetric(horizontal=12, vertical=8),
            "text_style": ft.TextStyle(size=14)
        }

        # Form fields
        title_field = ft.TextField(
            label="📝 Titolo Attività *",
            hint_text="Inserisci il titolo dell'attività",
            value=task.title if is_edit else (subtask_data.title if is_subtask else ""),
            expand=True,
            **field_style
        )

        description_field = ft.TextField(
            label="📄 Descrizione",
            hint_text="Descrizione dettagliata dell'attività",
            value=task.description if is_edit else (subtask_data.description if is_subtask else ""),
            multiline=True,
            min_lines=3,
            max_lines=5,
            expand=True,
            **field_style
        )

        # Priority dropdown with icons and colors
        priority_options = []
        priority_info = {
            "bassa": ("🟢", "Bassa", ft.Colors.GREEN_600),
            "media": ("🔵", "Media", ft.Colors.BLUE_600),
            "alta": ("🟠", "Alta", ft.Colors.ORANGE_600),
            "critica": ("🔴", "Critica", ft.Colors.RED_600)
        }

        for key, (icon, text, color) in priority_info.items():
            priority_options.append(
                ft.dropdown.Option(text=f"{icon} {text}", key=key)
            )

        # Fix the priority value extraction - handle both enum and string values
        current_priority = "media"  # default
        if is_edit and task.priority:
            try:
                if hasattr(task.priority, 'value'):
                    current_priority = task.priority.value
                else:
                    current_priority = str(task.priority)
            except Exception:
                current_priority = "media"  # fallback to default
        elif is_subtask and subtask_data.priority:
            try:
                if hasattr(subtask_data.priority, 'value'):
                    current_priority = subtask_data.priority.value
                else:
                    current_priority = str(subtask_data.priority)
            except Exception:
                current_priority = "media"  # fallback to default

        priority_dropdown = ft.Dropdown(
            label="⚡ Priorità *",
            options=priority_options,
            value=current_priority,
            width=180,
            **field_style
        )

        # Status dropdown with icons
        status_options = []
        status_info = {
            "in_attesa": ("⏳", "In Attesa"),
            "in_corso": ("🔄", "In Corso"),
            "in_pausa": ("⏸️", "In Pausa"),
            "completato": ("✅", "Completato"),
            "annullato": ("❌", "Annullato")
        }

        for key, (icon, text) in status_info.items():
            status_options.append(
                ft.dropdown.Option(text=f"{icon} {text}", key=key)
            )

        # Fix the status value extraction - handle both enum and string values
        current_status = "in_attesa"  # default
        if is_edit and task.status:
            try:
                if hasattr(task.status, 'value'):
                    current_status = task.status.value
                else:
                    current_status = str(task.status)
            except Exception:
                current_status = "in_attesa"  # fallback to default

        status_dropdown = ft.Dropdown(
            label="📊 Stato",
            options=status_options,
            value=current_status,
            width=180,
            **field_style
        )

        # Deadline dropdown with icons
        deadline_options = [ft.dropdown.Option(text="🚫 Nessuna scadenza specifica", key="")]
        for deadline_id, deadline in self.current_deadlines.items():
            # Get priority color for deadline
            priority_colors = {
                "critica": "🔴",
                "alta": "🟠",
                "media": "🔵",
                "bassa": "🟢"
            }
            deadline_priority = deadline.priority.value if hasattr(deadline.priority, 'value') else str(deadline.priority)
            priority_icon = priority_colors.get(deadline_priority, "📅")

            deadline_options.append(
                ft.dropdown.Option(
                    text=f"{priority_icon} {deadline.title} ({deadline.due_date.strftime('%d/%m/%Y')})",
                    key=str(deadline_id)
                )
            )

        deadline_dropdown = ft.Dropdown(
            label="🎯 Scadenza Associata (opzionale se progetto selezionato)",
            options=deadline_options,
            value=str(task.deadline_id) if is_edit and task.deadline_id else (str(subtask_data.deadline_id) if is_subtask and subtask_data.deadline_id else ""),
            expand=True,
            on_change=self._on_deadline_change,
            **field_style
        )

        # Project dropdown (shown when no deadline is selected)
        project_options = [ft.dropdown.Option(text="🚫 Nessun progetto specifico", key="")]
        for project_id, project in self.current_projects.items():
            project_options.append(
                ft.dropdown.Option(
                    text=f"🏗️ {project.name}",
                    key=str(project_id)
                )
            )

        project_dropdown = ft.Dropdown(
            label="🏗️ Progetto Diretto",
            options=project_options,
            value=str(task.project_id) if is_edit and task.project_id and not task.deadline_id else (str(subtask_data.project_id) if is_subtask and subtask_data.project_id and not subtask_data.deadline_id else ""),
            expand=True,
            visible=(is_edit and task.project_id and not task.deadline_id) or (is_subtask and subtask_data.project_id and not subtask_data.deadline_id),  # Show for subtasks with direct project inheritance
            **field_style
        )

        # Parent task dropdown (for subtasks)
        parent_task_options = [ft.dropdown.Option(text="🚫 Nessuna task padre", key="")]
        for existing_task in self.current_tasks:
            # Don't allow circular references and don't include the current task being edited
            if not is_edit or existing_task.id != task.id:
                if not existing_task.parent_task_id:  # Only show top-level tasks as potential parents
                    parent_task_options.append(
                        ft.dropdown.Option(
                            text=f"📋 {existing_task.title}",
                            key=str(existing_task.id)
                        )
                    )

        parent_task_dropdown = ft.Dropdown(
            label="📋 Task Padre (per sottotask)",
            options=parent_task_options,
            value=str(task.parent_task_id) if is_edit and task.parent_task_id else (str(subtask_data.parent_task_id) if is_subtask and subtask_data.parent_task_id else ""),
            expand=True,
            **field_style
        )

        # Button to toggle project dropdown visibility
        show_project_button = ft.ElevatedButton(
            text="➕ Aggiungi a Progetto Direttamente",
            icon=ft.Icons.FOLDER_OPEN,
            style=ft.ButtonStyle(
                color=ft.Colors.BLUE_600,
                bgcolor=ft.Colors.BLUE_50,
            ),
            visible=not ((is_edit and task.project_id and not task.deadline_id) or (is_subtask and subtask_data.project_id and not subtask_data.deadline_id)),  # Hidden when editing direct project task or subtask with project
            expand=True
        )

        # Button to cancel direct project mode and go back to deadline mode
        cancel_project_button = ft.OutlinedButton(
            text="↩️ Torna a Scadenza",
            icon=ft.Icons.SCHEDULE,
            style=ft.ButtonStyle(
                color=ft.Colors.GREY_600,
                side=ft.BorderSide(1, ft.Colors.GREY_300),
            ),
            visible=(is_edit and task.project_id and not task.deadline_id) or (is_subtask and subtask_data.project_id and not subtask_data.deadline_id),  # Shown when editing direct project task or subtask with project
            expand=True
        )

        # Due date picker with time support
        from ui.components.datetime_picker import DateTimePicker
        due_date_picker = DateTimePicker(
            label="📅 Data e Ora Scadenza Task",
            value=task.due_date if is_edit else None,
            include_time=True,
            width=400,
            page=self.app.page
        )

        # Estimated hours
        estimated_hours_field = ft.TextField(
            label="⏱️ Ore Stimate",
            hint_text="0",
            value=str(task.estimated_hours) if is_edit and task.estimated_hours else "",
            keyboard_type=ft.KeyboardType.NUMBER,
            width=140,
            **field_style
        )

        # Actual hours
        actual_hours_field = ft.TextField(
            label="⏰ Ore Effettive",
            hint_text="0",
            value=str(task.actual_hours) if is_edit and task.actual_hours else "",
            keyboard_type=ft.KeyboardType.NUMBER,
            width=140,
            **field_style
        )

        # Progress percentage
        progress_field = ft.TextField(
            label="📊 Progresso (%)",
            hint_text="0",
            value=str(task.progress_percentage) if is_edit else "0",
            keyboard_type=ft.KeyboardType.NUMBER,
            width=140,
            **field_style
        )

        # Tags field
        tags_field = ft.TextField(
            label="🏷️ Tag (separati da virgola)",
            hint_text="tag1, tag2, tag3",
            value=", ".join(task.tags) if is_edit and task.tags else "",
            expand=True,
            **field_style
        )

        def _on_deadline_change_internal(e):
            """Handle deadline dropdown changes"""
            has_deadline = bool(e.control.value)
            
            if has_deadline:
                # Hide project dropdown and show button when deadline is selected
                project_dropdown.visible = False
                project_dropdown.value = ""  # Clear project selection
                show_project_button.visible = False
                cancel_project_button.visible = False
                deadline_dropdown.label = "🎯 Scadenza Associata"
            else:
                # Show button to allow project selection when no deadline
                show_project_button.visible = True
                cancel_project_button.visible = False
                deadline_dropdown.label = "🎯 Scadenza Associata"
            
            # Update the dialog content
            self.app.page.update()

        def _on_project_change_internal(e):
            """Handle project dropdown changes"""
            has_project = bool(e.control.value)
            
            if has_project:
                # Update deadline label to show it's optional when project is selected
                deadline_dropdown.label = "🎯 Scadenza Associata (opzionale)"
            else:
                deadline_dropdown.label = "🎯 Scadenza Associata"
            
            # Update the dialog content
            self.app.page.update()

        def _on_show_project_click(e):
            """Show project dropdown when button is clicked"""
            project_dropdown.visible = True
            show_project_button.visible = False
            cancel_project_button.visible = True
            
            # Clear deadline selection when switching to direct project mode
            deadline_dropdown.value = ""
            deadline_dropdown.label = "🎯 Scadenza Associata (opzionale)"
            
            # Update the dialog content
            self.app.page.update()

        def _on_cancel_project_click(e):
            """Cancel direct project mode and go back to deadline mode"""
            project_dropdown.visible = False
            project_dropdown.value = ""  # Clear project selection
            show_project_button.visible = True
            cancel_project_button.visible = False
            
            # Reset deadline label
            deadline_dropdown.label = "🎯 Scadenza Associata"
            
            # Update the dialog content
            self.app.page.update()

        # Set the change handlers
        deadline_dropdown.on_change = _on_deadline_change_internal
        project_dropdown.on_change = _on_project_change_internal
        show_project_button.on_click = _on_show_project_click
        cancel_project_button.on_click = _on_cancel_project_click

        def save_task(e):
            # Validate required fields
            if not title_field.value.strip():
                self._show_error_dialog("Errore", "Il titolo è obbligatorio")
                return

            try:
                # Parse fields
                due_date = due_date_picker.value if due_date_picker.value else None
                deadline_id = UUID(deadline_dropdown.value) if deadline_dropdown.value else None
                direct_project_id = UUID(project_dropdown.value) if project_dropdown.value else None
                parent_task_id = UUID(parent_task_dropdown.value) if parent_task_dropdown.value else None

                # Allow tasks without any association, but if they want association, need at least one
                # This is now optional - tasks can be standalone, project-specific, or deadline-specific

                estimated_hours = float(estimated_hours_field.value) if estimated_hours_field.value else None
                actual_hours = float(actual_hours_field.value) if actual_hours_field.value else None
                progress = int(progress_field.value) if progress_field.value else 0
                tags = [tag.strip() for tag in tags_field.value.split(",") if tag.strip()] if tags_field.value else []

                # Determine project_id and deadline_id (with inheritance from parent task)
                project_id = None
                if parent_task_id:
                    # Inherit from parent task
                    parent_task = next((t for t in self.current_tasks if t.id == parent_task_id), None)
                    if parent_task:
                        if not deadline_id and parent_task.deadline_id:
                            deadline_id = parent_task.deadline_id
                        if not direct_project_id and parent_task.project_id:
                            project_id = parent_task.project_id
                
                if deadline_id:
                    # Get project_id from deadline
                    if deadline_id in self.current_deadlines:
                        deadline = self.current_deadlines[deadline_id]
                        project_id = deadline.project_id
                elif direct_project_id:
                    # Use direct project assignment
                    project_id = direct_project_id
                # If neither deadline nor direct project, project_id remains None (standalone task)
                
                if is_edit:
                    # Update existing task
                    task.title = title_field.value.strip()
                    task.description = description_field.value.strip()
                    task.priority = Priority(priority_dropdown.value)
                    task.status = TaskStatus(status_dropdown.value)
                    task.deadline_id = deadline_id
                    task.project_id = project_id
                    task.parent_task_id = parent_task_id
                    task.due_date = due_date
                    task.estimated_hours = estimated_hours
                    task.actual_hours = actual_hours
                    task.progress_percentage = progress
                    task.tags = tags
                    task.updated_at = datetime.now()

                    success = self.db_manager.update_task(task)

                    # Update parent task progress if this is a subtask
                    if success and task.parent_task_id:
                        self.db_manager.update_parent_task_progress(task.parent_task_id)

                    # Auto-sync to Google Tasks with improved feedback
                    sync_success = True
                    sync_message = ""
                    if success and self._should_auto_sync():
                        sync_success = self._auto_sync_task_to_google(task, "update")
                        if sync_success:
                            sync_message = " (Sincronizzata con Google Tasks)"
                        else:
                            sync_message = " (Errore sincronizzazione Google Tasks)"
                            logger.warning(f"Task updated but Google sync failed: {task.title}")

                    action_text = "aggiornata"
                    message = f"Attività {action_text} con successo!{sync_message}"
                else:
                    # Create new task
                    new_task = Task(
                        id=uuid4(),
                        title=title_field.value.strip(),
                        description=description_field.value.strip(),
                        priority=Priority(priority_dropdown.value),
                        status=TaskStatus(status_dropdown.value),
                        deadline_id=deadline_id,
                        project_id=project_id,
                        parent_task_id=parent_task_id,
                        due_date=due_date,
                        estimated_hours=estimated_hours,
                        actual_hours=actual_hours,
                        progress_percentage=progress,
                        tags=tags,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    
                    success = self.db_manager.create_task(new_task)

                    # Update parent task progress if this is a subtask
                    if success and new_task.parent_task_id:
                        self.db_manager.update_parent_task_progress(new_task.parent_task_id)

                    # Auto-sync to Google Tasks with improved feedback
                    sync_success = True
                    sync_message = ""
                    if success and self._should_auto_sync():
                        sync_success = self._auto_sync_task_to_google(new_task, "create")
                        if sync_success:
                            sync_message = " (Sincronizzata con Google Tasks)"
                        else:
                            sync_message = " (Errore sincronizzazione Google Tasks)"
                            logger.warning(f"Task created but Google sync failed: {new_task.title}")

                    action_text = "creata"
                    message = f"Attività {action_text} con successo!{sync_message}"
                
                if success:
                    self.app.page.close(dialog)
                    self.refresh_data()
                    self._show_success_dialog("Successo", message)
                else:
                    self._show_error_dialog("Errore", f"Errore durante il salvataggio dell'attività")
                    
            except ValueError as ve:
                self._show_error_dialog("Errore", f"Valore non valido: {str(ve)}")
            except Exception as ex:
                logger.error(f"Errore salvataggio task: {ex}")
                self._show_error_dialog("Errore", f"Errore imprevisto: {str(ex)}")

        def close_dialog(e):
            self.app.page.close(dialog)

        # Create modern dialog
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.TASK_ALT, color=ft.Colors.WHITE, size=24),
                    ft.Text(
                        title,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.WHITE,
                        size=18
                    )
                ], spacing=12),
                padding=ft.padding.all(20),
                gradient=ft.LinearGradient(
                    colors=[ft.Colors.TEAL_600, ft.Colors.TEAL_800],
                    begin=ft.alignment.top_left,
                    end=ft.alignment.bottom_right
                ),
                border_radius=ft.border_radius.only(top_left=12, top_right=12),
                margin=ft.margin.only(left=-24, right=-24, top=-24)
            ),
            content=ft.Container(
                content=ft.ListView([
                    # Basic info section
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.INFO_OUTLINE, color=ft.Colors.TEAL_600, size=20),
                                ft.Text(
                                    "Informazioni Base",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=ft.Colors.GREY_800
                                )
                            ], spacing=8),
                            ft.Container(height=16),
                            title_field,
                            ft.Container(height=12),
                            description_field,
                            ft.Container(height=12),
                            ft.Row([
                                priority_dropdown,
                                status_dropdown
                            ], spacing=16),
                            ft.Container(height=12),
                            deadline_dropdown,
                            ft.Container(height=8),
                            show_project_button,
                            project_dropdown,
                            cancel_project_button,
                            ft.Container(height=8),
                            parent_task_dropdown,
                            ft.Container(height=8),
                            ft.Text(
                                "💡 Scegli una scadenza per il workflow tradizionale, o clicca il pulsante per assegnare direttamente a un progetto",
                                size=12,
                                color=ft.Colors.GREY_600,
                                italic=True
                            )
                        ]),
                        padding=ft.padding.all(20),
                        bgcolor=ft.Colors.WHITE,
                        border=ft.border.all(1, ft.Colors.GREY_200),
                        border_radius=12,
                        margin=ft.margin.only(bottom=16),
                        shadow=ft.BoxShadow(
                            spread_radius=0,
                            blur_radius=4,
                            color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                            offset=ft.Offset(0, 2)
                        )
                    ),

                    # Time & Progress section
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.SCHEDULE, color=ft.Colors.BLUE_600, size=20),
                                ft.Text(
                                    "Tempo e Progresso",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=ft.Colors.GREY_800
                                )
                            ], spacing=8),
                            ft.Container(height=16),
                            ft.Row([
                                due_date_picker,
                                progress_field
                            ], spacing=16),
                            ft.Container(height=8),
                            ft.Container(
                                content=ft.Text(
                                    "ℹ️ L'orario viene salvato localmente ma Google Tasks sincronizza solo la data",
                                    size=11,
                                    color=ft.Colors.BLUE_600,
                                    italic=True
                                ),
                                padding=ft.padding.all(8),
                                bgcolor=ft.Colors.BLUE_50,
                                border_radius=6,
                                border=ft.border.all(1, ft.Colors.BLUE_200)
                            ),
                            ft.Container(height=12),
                            ft.Row([
                                estimated_hours_field,
                                actual_hours_field
                            ], spacing=16),
                            ft.Container(height=8),
                            ft.Text(
                                "💡 Le ore effettive vengono aggiornate automaticamente durante il lavoro",
                                size=12,
                                color=ft.Colors.GREY_600,
                                italic=True
                            )
                        ]),
                        padding=ft.padding.all(20),
                        bgcolor=ft.Colors.WHITE,
                        border=ft.border.all(1, ft.Colors.GREY_200),
                        border_radius=12,
                        margin=ft.margin.only(bottom=16),
                        shadow=ft.BoxShadow(
                            spread_radius=0,
                            blur_radius=4,
                            color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                            offset=ft.Offset(0, 2)
                        )
                    ),

                    # Additional info section
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.LABEL, color=ft.Colors.ORANGE_600, size=20),
                                ft.Text(
                                    "Informazioni Aggiuntive",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=ft.Colors.GREY_800
                                )
                            ], spacing=8),
                            ft.Container(height=16),
                            tags_field,
                            ft.Container(height=8),
                            ft.Text(
                                "💡 Usa i tag per organizzare e filtrare le tue attività",
                                size=12,
                                color=ft.Colors.GREY_600,
                                italic=True
                            )
                        ]),
                        padding=ft.padding.all(20),
                        bgcolor=ft.Colors.WHITE,
                        border=ft.border.all(1, ft.Colors.GREY_200),
                        border_radius=12,
                        shadow=ft.BoxShadow(
                            spread_radius=0,
                            blur_radius=4,
                            color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                            offset=ft.Offset(0, 2)
                        )
                    )
                ], spacing=0),
                width=650,
                height=550,
                padding=ft.padding.all(20),
                bgcolor=ft.Colors.GREY_50
            ),
            actions=[
                ft.OutlinedButton(
                    content=ft.Row([
                        ft.Icon(ft.Icons.CLOSE, size=16),
                        ft.Text("Annulla", size=14, weight=ft.FontWeight.W_500)
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    on_click=close_dialog,
                    style=ft.ButtonStyle(
                        color=ft.Colors.GREY_700,
                        side=ft.BorderSide(2, ft.Colors.GREY_300),
                        padding=ft.padding.symmetric(horizontal=24, vertical=12),
                        shape=ft.RoundedRectangleBorder(radius=8)
                    ),
                    height=44,
                    width=120
                ),
                ft.ElevatedButton(
                    content=ft.Row([
                        ft.Icon(
                            ft.Icons.SAVE if not is_edit else ft.Icons.UPDATE,
                            size=16,
                            color=ft.Colors.WHITE
                        ),
                        ft.Text(
                            "Salva" if not is_edit else "Aggiorna",
                            size=14,
                            weight=ft.FontWeight.W_600,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    on_click=save_task,
                    style=ft.ButtonStyle(
                        bgcolor=ft.Colors.TEAL_600,
                        color=ft.Colors.WHITE,
                        padding=ft.padding.symmetric(horizontal=24, vertical=12),
                        elevation=3,
                        shadow_color=ft.Colors.TEAL_200,
                        shape=ft.RoundedRectangleBorder(radius=8)
                    ),
                    height=44,
                    width=140
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END,
            actions_padding=ft.padding.all(20),
            bgcolor=ft.Colors.GREY_50,
            shape=ft.RoundedRectangleBorder(radius=12),
            elevation=8
        )
        
        self.app.page.open(dialog)
        logger.info(f"Task form opened for: {task.title if task else 'new task'}")

    def _on_deadline_change(self, e):
        """Handle deadline dropdown changes to show/hide project dropdown"""
        # This method is for compatibility, the actual handler is defined inline
        pass

    def cancel_current_form(self):
        """Cancel current form - called by keyboard shortcuts"""
        # Check if there's an open dialog
        if hasattr(self.app, 'page') and hasattr(self.app.page, 'overlay'):
            # Close any open dialogs
            for overlay in self.app.page.overlay:
                if hasattr(overlay, 'open') and overlay.open:
                    overlay.open = False
            self.app.page.update()
            logger.info("Task form cancelled via keyboard shortcut")

    def save_current_form(self):
        """Save current form - called by keyboard shortcuts"""
        # For task forms, since they use dialogs with complex validation,
        # we show a helpful message
        logger.info("Save shortcut triggered in task form")
        if hasattr(self, 'app') and hasattr(self.app, 'main_layout'):
            self.app.main_layout._show_shortcut_feedback("Use the Save button in the task form or focus on a field and press Ctrl+S")
    
    def _show_success_dialog(self, title: str, message: str):
        """Show success dialog"""
        def close_dialog(e):
            self.app.page.close(success_dialog)
        
        success_dialog = ft.AlertDialog(
            title=ft.Text(title, color=ft.Colors.GREEN),
            content=ft.Text(message),
            actions=[ft.TextButton("OK", on_click=close_dialog)]
        )
        self.app.page.open(success_dialog)
    
    def _show_error_dialog(self, title: str, message: str):
        """Show error dialog"""
        def close_dialog(e):
            self.app.page.close(error_dialog)
        
        error_dialog = ft.AlertDialog(
            title=ft.Text(title, color=ft.Colors.RED),
            content=ft.Text(message),
            actions=[ft.TextButton("OK", on_click=close_dialog)]
        )
        self.app.page.open(error_dialog)
    
    def _edit_task(self, task: Task):
        """Edit a task"""
        self._show_task_form(task=task)

    def _create_subtask(self, parent_task: Task):
        """Create a subtask for the given parent task"""
        # Create a new task structure for the form (without validation issues)
        from dataclasses import dataclass
        from typing import Optional

        @dataclass
        class TaskFormData:
            id: Optional[UUID] = None
            title: str = ""
            description: str = ""
            parent_task_id: Optional[UUID] = None
            project_id: Optional[UUID] = None
            deadline_id: Optional[UUID] = None
            priority: Priority = Priority.MEDIUM
            status: TaskStatus = TaskStatus.PENDING

        # Create form data with inherited values
        subtask_data = TaskFormData(
            parent_task_id=parent_task.id,
            project_id=parent_task.project_id,
            deadline_id=parent_task.deadline_id,
            priority=parent_task.priority,
            status=TaskStatus.PENDING
        )

        # Show the dedicated subtask form instead of the general task form
        self._show_subtask_form(parent_task, subtask_data)

    def _show_subtask_form(self, parent_task: Task, subtask_data=None):
        """Show dedicated form for creating/editing subtasks with simplified UI"""

        # Stile comune per i campi
        field_style = {
            "bgcolor": ft.Colors.WHITE,
            "border_color": ft.Colors.GREY_300,
            "border_radius": 8,
            "content_padding": ft.padding.symmetric(horizontal=12, vertical=8),
            "text_style": ft.TextStyle(size=14)
        }

        # Simplified form fields for subtasks
        title_field = ft.TextField(
            label="📝 Titolo Sottotask *",
            hint_text="Inserisci il titolo della sottotask",
            value=subtask_data.title if subtask_data else "",
            expand=True,
            **field_style
        )

        description_field = ft.TextField(
            label="📄 Descrizione",
            hint_text="Descrizione dettagliata della sottotask",
            value=subtask_data.description if subtask_data else "",
            multiline=True,
            min_lines=2,
            max_lines=4,
            expand=True,
            **field_style
        )

        # Priority dropdown (simplified)
        priority_options = [
            ft.dropdown.Option(text="🟢 Bassa", key="bassa"),
            ft.dropdown.Option(text="🔵 Media", key="media"),
            ft.dropdown.Option(text="🟠 Alta", key="alta"),
            ft.dropdown.Option(text="🔴 Critica", key="critica")
        ]

        priority_dropdown = ft.Dropdown(
            label="⚡ Priorità",
            options=priority_options,
            value=subtask_data.priority.value if subtask_data and hasattr(subtask_data.priority, 'value') else "media",
            width=180,
            **field_style
        )

        # Due date picker with time support
        from ui.components.datetime_picker import DateTimePicker
        due_date_picker = DateTimePicker(
            label="📅 Data e Ora Scadenza",
            value=None,
            include_time=True,
            width=400,
            page=self.app.page
        )

        # Estimated hours
        estimated_hours_field = ft.TextField(
            label="⏱️ Ore Stimate",
            hint_text="0",
            value="",
            keyboard_type=ft.KeyboardType.NUMBER,
            width=140,
            **field_style
        )

        def save_subtask(e):
            # Validate required fields
            if not title_field.value.strip():
                self._show_error_dialog("Errore", "Il titolo è obbligatorio")
                return

            try:
                # Parse fields
                due_date = due_date_picker.value if due_date_picker.value else None
                estimated_hours = float(estimated_hours_field.value) if estimated_hours_field.value else None

                # Create new subtask
                new_subtask = Task(
                    id=uuid4(),
                    title=title_field.value.strip(),
                    description=description_field.value.strip(),
                    priority=Priority(priority_dropdown.value),
                    status=TaskStatus.PENDING,
                    deadline_id=parent_task.deadline_id,  # Inherit from parent
                    project_id=parent_task.project_id,    # Inherit from parent
                    parent_task_id=parent_task.id,        # Set parent relationship
                    due_date=due_date,
                    estimated_hours=estimated_hours,
                    actual_hours=None,
                    progress_percentage=0,
                    tags=[],
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )

                success = self.db_manager.create_task(new_subtask)

                # Update parent task progress
                if success:
                    self.db_manager.update_parent_task_progress(parent_task.id)

                # Auto-sync to Google Tasks
                sync_success = True
                sync_message = ""
                if success and self._should_auto_sync():
                    sync_success = self._auto_sync_task_to_google(new_subtask, "create")
                    if sync_success:
                        sync_message = " (Sincronizzata con Google Tasks)"
                    else:
                        sync_message = " (Errore sincronizzazione Google Tasks)"
                        logger.warning(f"Subtask created but Google sync failed: {new_subtask.title}")

                if success:
                    self.app.page.close(dialog)
                    self.refresh_data()
                    self._show_success_dialog("Successo", f"Sottotask creata con successo!{sync_message}")
                else:
                    self._show_error_dialog("Errore", "Errore durante il salvataggio della sottotask")

            except ValueError as ve:
                self._show_error_dialog("Errore", f"Valore non valido: {str(ve)}")
            except Exception as ex:
                logger.error(f"Errore salvataggio subtask: {ex}")
                self._show_error_dialog("Errore", f"Errore imprevisto: {str(ex)}")

        def close_dialog(e):
            self.app.page.close(dialog)

        # Get parent task info for display
        deadline = self.current_deadlines.get(parent_task.deadline_id) if parent_task.deadline_id else None
        project = self.current_projects.get(parent_task.project_id) if parent_task.project_id else None

        # Create simplified dialog for subtasks
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.SUBDIRECTORY_ARROW_RIGHT, color=ft.Colors.WHITE, size=24),
                    ft.Text(
                        "Nuova Sottotask",
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.WHITE,
                        size=18
                    )
                ], spacing=12),
                padding=ft.padding.all(20),
                gradient=ft.LinearGradient(
                    colors=[ft.Colors.PURPLE_600, ft.Colors.PURPLE_800],
                    begin=ft.alignment.top_left,
                    end=ft.alignment.bottom_right
                ),
                border_radius=ft.border_radius.only(top_left=12, top_right=12),
                margin=ft.margin.only(left=-24, right=-24, top=-24)
            ),
            content=ft.Container(
                content=ft.ListView([
                    # Parent task info section
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.INFO_OUTLINE, color=ft.Colors.PURPLE_600, size=20),
                                ft.Text(
                                    "Task Padre",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=ft.Colors.GREY_800
                                )
                            ], spacing=8),
                            ft.Container(height=12),
                            ft.Container(
                                content=ft.Column([
                                    ft.Text(f"📋 {parent_task.title}", size=14, weight=ft.FontWeight.BOLD),
                                    ft.Text(f"🎯 {deadline.title if deadline else 'Nessuna scadenza'}", size=12, color=ft.Colors.GREY_600),
                                    ft.Text(f"🏗️ {project.name if project else 'Nessun progetto'}", size=12, color=ft.Colors.GREY_600),
                                ]),
                                padding=ft.padding.all(12),
                                bgcolor=ft.Colors.PURPLE_50,
                                border_radius=8,
                                border=ft.border.all(1, ft.Colors.PURPLE_200)
                            )
                        ]),
                        padding=ft.padding.all(20),
                        bgcolor=ft.Colors.WHITE,
                        border=ft.border.all(1, ft.Colors.GREY_200),
                        border_radius=12,
                        margin=ft.margin.only(bottom=16)
                    ),

                    # Subtask details section
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.TASK_ALT, color=ft.Colors.BLUE_600, size=20),
                                ft.Text(
                                    "Dettagli Sottotask",
                                    size=16,
                                    weight=ft.FontWeight.W_600,
                                    color=ft.Colors.GREY_800
                                )
                            ], spacing=8),
                            ft.Container(height=16),
                            title_field,
                            ft.Container(height=12),
                            description_field,
                            ft.Container(height=12),
                            ft.Row([
                                priority_dropdown,
                                due_date_picker
                            ], spacing=16),
                            ft.Container(height=8),
                            ft.Container(
                                content=ft.Text(
                                    "ℹ️ L'orario viene salvato localmente ma Google Tasks sincronizza solo la data",
                                    size=11,
                                    color=ft.Colors.BLUE_600,
                                    italic=True
                                ),
                                padding=ft.padding.all(8),
                                bgcolor=ft.Colors.BLUE_50,
                                border_radius=6,
                                border=ft.border.all(1, ft.Colors.BLUE_200)
                            ),
                            ft.Container(height=12),
                            estimated_hours_field,
                            ft.Container(height=8),
                            ft.Text(
                                "💡 La sottotask erediterà automaticamente progetto e scadenza dal task padre",
                                size=12,
                                color=ft.Colors.GREY_600,
                                italic=True
                            )
                        ]),
                        padding=ft.padding.all(20),
                        bgcolor=ft.Colors.WHITE,
                        border=ft.border.all(1, ft.Colors.GREY_200),
                        border_radius=12
                    )
                ], spacing=0),
                width=550,
                height=450,
                padding=ft.padding.all(20),
                bgcolor=ft.Colors.GREY_50
            ),
            actions=[
                ft.OutlinedButton(
                    content=ft.Row([
                        ft.Icon(ft.Icons.CLOSE, size=16),
                        ft.Text("Annulla", size=14, weight=ft.FontWeight.W_500)
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    on_click=close_dialog,
                    style=ft.ButtonStyle(
                        color=ft.Colors.GREY_700,
                        side=ft.BorderSide(2, ft.Colors.GREY_300),
                        padding=ft.padding.symmetric(horizontal=24, vertical=12),
                        shape=ft.RoundedRectangleBorder(radius=8)
                    ),
                    height=44,
                    width=120
                ),
                ft.ElevatedButton(
                    content=ft.Row([
                        ft.Icon(ft.Icons.ADD_TASK, size=16, color=ft.Colors.WHITE),
                        ft.Text(
                            "Crea Sottotask",
                            size=14,
                            weight=ft.FontWeight.W_600,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    on_click=save_subtask,
                    style=ft.ButtonStyle(
                        bgcolor=ft.Colors.PURPLE_600,
                        color=ft.Colors.WHITE,
                        padding=ft.padding.symmetric(horizontal=24, vertical=12),
                        elevation=3,
                        shadow_color=ft.Colors.PURPLE_200,
                        shape=ft.RoundedRectangleBorder(radius=8)
                    ),
                    height=44,
                    width=160
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END,
            actions_padding=ft.padding.all(20),
            bgcolor=ft.Colors.GREY_50,
            shape=ft.RoundedRectangleBorder(radius=12),
            elevation=8
        )

        self.app.page.open(dialog)
        logger.info(f"Subtask form opened for parent: {parent_task.title}")
    
    def _toggle_task_completion(self, task: Task):
        """Toggle task completion status"""
        try:
            current_status = task.status.value if hasattr(task.status, 'value') else str(task.status)
            
            if current_status == "completato":
                task.status = TaskStatus.IN_PROGRESS
                task.progress_percentage = max(0, task.progress_percentage - 10)  # Reduce progress a bit
                action = "riaperta"
                message = f"Attività '{task.title}' riaperta"
            else:
                task.status = TaskStatus.COMPLETED
                task.progress_percentage = 100
                action = "completata"
                message = f"Attività '{task.title}' completata!"
            
            task.updated_at = datetime.now()
            success = self.db_manager.update_task(task)

            # Auto-sync to Google Tasks with improved feedback
            sync_success = True
            sync_message = ""
            if success and self._should_auto_sync():
                sync_success = self._auto_sync_task_to_google(task, "update")
                if sync_success:
                    sync_message = " (Sincronizzata con Google Tasks)"
                else:
                    sync_message = " (Errore sincronizzazione Google Tasks)"
                    logger.warning(f"Task {action} but Google sync failed: {task.title}")

            if success:
                self.refresh_data()
                self._show_success_dialog("Successo", f"{message}{sync_message}")
                logger.info(f"Task {action}: {task.title}")
            else:
                self._show_error_dialog("Errore", f"Errore durante l'aggiornamento dell'attività")
            
        except Exception as e:
            logger.error(f"Errore toggle completamento task: {e}")
            self._show_error_dialog("Errore", f"Errore imprevisto: {str(e)}")
    
    def _delete_task(self, task: Task):
        """Delete a task with confirmation"""
        def confirm_delete(e):
            self.app.page.close(confirm_dialog)
            
            try:
                # Auto-sync deletion to Google Tasks (before deleting from local DB)
                sync_success = True
                sync_message = ""
                if self._should_auto_sync():
                    sync_success = self._auto_sync_task_to_google(task, "delete")
                    if sync_success:
                        sync_message = " (Eliminata anche da Google Tasks)"
                    else:
                        sync_message = " (Errore eliminazione da Google Tasks)"
                        logger.warning(f"Task deletion from Google failed: {task.title}")

                # Delete from local database
                success = self.db_manager.delete_task(task.id)

                if success:
                    self.refresh_data()
                    self._show_success_dialog("Successo", f"Attività '{task.title}' eliminata con successo!{sync_message}")
                    logger.info(f"Task eliminata: {task.title}")
                else:
                    self._show_error_dialog("Errore", "Errore durante l'eliminazione dell'attività")
                    
            except Exception as ex:
                logger.error(f"Errore eliminazione task: {ex}")
                self._show_error_dialog("Errore", f"Errore imprevisto: {str(ex)}")
        
        def cancel_delete(e):
            self.app.page.close(confirm_dialog)
        
        confirm_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Conferma eliminazione", color=ft.Colors.RED),
            content=ft.Text(f"Sei sicuro di voler eliminare l'attività '{task.title}'?\n\nQuesta azione non può essere annullata."),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_delete),
                ft.ElevatedButton(
                    "Elimina",
                    on_click=confirm_delete,
                    bgcolor=ft.Colors.RED,
                    color=ft.Colors.WHITE
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )
        
        self.app.page.open(confirm_dialog)
    
    def _sync_google_tasks(self, e):
        """Sync tasks with Google Tasks"""
        # Force refresh the Google Tasks status before checking
        self._refresh_google_tasks_status()

        if not self._is_google_sync_enabled():
            # Get detailed status for better error message
            service_status = self.google_tasks_service.get_authentication_status()
            settings = self.google_tasks_settings

            error_details = (
                f"Stato servizio Google Tasks:\n"
                f"• Librerie disponibili: {service_status.get('google_available', False)}\n"
                f"• Servizio abilitato: {service_status.get('service_enabled', False)}\n"
                f"• Token esistente: {service_status.get('token_file_exists', False)}\n"
                f"• Impostazioni abilitate: {settings.get('tasks_enabled', False)}\n"
                f"• Impostazioni autenticate: {settings.get('tasks_authenticated', False)}"
            )

            self._show_info_dialog(
                "Google Tasks Non Configurato",
                f"Google Tasks non è configurato o autenticato.\n\n{error_details}\n\n"
                "Vai nelle Impostazioni > Servizi Google per configurare l'integrazione."
            )
            logger.info(f"Google Tasks not configured - {error_details}")
            return
        
        # Show loading dialog
        self._show_loading_dialog("Sincronizzazione con Google Tasks in corso...")
        
        try:
            # Perform the sync
            results = self._sync_all_tasks_to_google()
            
            # Close loading dialog
            self.app.page.overlay.clear()
            self.app.page.update()
            
            # Show results
            if results["success"]:
                message = (f"Sincronizzazione completata!\n\n"
                          f"• Task sincronizzate: {results['synced']}\n"
                          f"• Task già sincronizzate: {results['skipped']}\n"
                          f"• Totale task: {results['total']}")
                
                if results["errors"]:
                    message += f"\n• Errori: {len(results['errors'])}"
                
                self._show_success_dialog("Sincronizzazione Completata", message)
                
                # Refresh data to show updated sync status
                self.refresh_data()
            else:
                error_msg = results.get("error", "Errore sconosciuto")
                self._show_error_dialog("Errore Sincronizzazione", f"Errore durante la sincronizzazione:\n{error_msg}")
            
        except Exception as ex:
            # Close loading dialog in case of error
            try:
                self.app.page.overlay.clear()
                self.app.page.update()
            except:
                pass
            
            logger.error(f"Errore sincronizzazione Google Tasks: {ex}")
            self._show_error_dialog("Errore", f"Errore imprevisto durante la sincronizzazione:\n{str(ex)}")
    
    def _show_info_dialog(self, title: str, message: str):
        """Show info dialog"""
        def close_dialog(e):
            self.app.page.close(info_dialog)
        
        info_dialog = ft.AlertDialog(
            title=ft.Text(title, color=ft.Colors.BLUE),
            content=ft.Text(message),
            actions=[ft.TextButton("OK", on_click=close_dialog)]
        )
        self.app.page.open(info_dialog)
    
    def _show_loading_dialog(self, message: str):
        """Show loading dialog"""
        loading_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Row([
                ft.ProgressRing(width=20, height=20),
                ft.Text("Sincronizzazione", weight=ft.FontWeight.BOLD)
            ], spacing=12),
            content=ft.Text(message),
            actions=[]
        )
        self.app.page.open(loading_dialog)
    
    def _has_subtasks(self, task: Task) -> bool:
        """Check if task has subtasks"""
        try:
            subtasks = self.db_manager.get_subtasks(task.id)
            return len(subtasks) > 0
        except Exception as e:
            logger.error(f"Error checking subtasks for task {task.title}: {e}")
            return False
    
    def _sync_task_hierarchy_to_google(self, task: Task) -> bool:
        """
        Sync hierarchical tasks (with parent or children) to Google Tasks
        
        This method handles the synchronization of task hierarchies while ensuring
        proper parent-child relationships are maintained in Google Tasks.
        
        Args:
            task: The task to sync (can be parent or child)
            
        Returns:
            True if sync successful, False otherwise
        """
        try:
            # For now, treat hierarchical tasks the same as regular tasks
            # Google Tasks API has limited hierarchy support, so we'll sync them flat
            # but include hierarchy info in the task description/notes
            
            if task.parent_task_id:
                # This is a subtask - include parent info in description
                parent_task = next((t for t in self.current_tasks if t.id == task.parent_task_id), None)
                if parent_task:
                    # Add parent task reference to description
                    original_desc = task.description or ""
                    hierarchy_note = f"\n[Sottotask di: {parent_task.title}]"
                    if hierarchy_note not in original_desc:
                        # Temporarily modify description for sync
                        task.description = (original_desc + hierarchy_note).strip()
            
            # Use regular sync method
            success = self._sync_single_task_to_google(task)
            
            # If this task has subtasks, sync them too
            if success and self._has_subtasks(task):
                subtasks = self.db_manager.get_subtasks(task.id)
                for subtask in subtasks:
                    subtask_success = self._sync_single_task_to_google(subtask)
                    if not subtask_success:
                        logger.warning(f"Failed to sync subtask: {subtask.title}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error syncing task hierarchy for '{task.title}': {e}")
            return False
    
    def _sync_single_task_to_google(self, task: Task) -> bool:
        """
        Sync a single task to Google Tasks without hierarchy handling
        
        This is a helper method that contains the core sync logic without 
        the hierarchy-specific handling.
        """
        try:
            # Get associated deadline or create virtual deadline for direct project tasks
            deadline = None
            if task.deadline_id:
                deadline = self.current_deadlines.get(task.deadline_id)
            else:
                # For direct project tasks, create a virtual deadline for Google sync
                project = self.current_projects.get(task.project_id) if task.project_id else None
                if project:
                    # Create a virtual deadline representation for sync purposes
                    from dataclasses import dataclass
                    from datetime import date, timedelta
                    
                    @dataclass
                    class VirtualDeadline:
                        id: UUID
                        title: str
                        project_id: UUID
                        due_date: date = date.today() + timedelta(days=30)  # Default 30 days
                    
                    deadline = VirtualDeadline(
                        id=task.project_id,  # Use project ID as deadline ID for caching
                        title=f"Tasks - {project.name}",
                        project_id=task.project_id
                    )
                else:
                    # For standalone tasks, create a generic virtual deadline
                    from dataclasses import dataclass
                    from datetime import date, timedelta
                    from uuid import uuid4
                    
                    @dataclass
                    class VirtualDeadline:
                        id: UUID
                        title: str
                        project_id: Optional[UUID] = None
                        due_date: date = date.today() + timedelta(days=30)
                    
                    deadline = VirtualDeadline(
                        id=uuid4(),  # Generate a unique ID for standalone tasks
                        title="Standalone Tasks",
                        project_id=None
                    )

            if not deadline:
                logger.error(f"Could not determine deadline/container for task: {task.title}")
                return False

            # Create or update - ensure we have a Google Tasks list for the deadline/project
            google_list_id = self._get_or_create_google_list(deadline)
            if not google_list_id:
                logger.error(f"Failed to get/create Google Tasks list for '{deadline.title}'")
                return False

            # Sync the task
            google_task_id = self.google_tasks_service.sync_task_to_google(task, deadline)
            if google_task_id:
                # Update local task with Google IDs if changed
                if task.google_task_id != google_task_id or task.google_task_list_id != google_list_id:
                    task.google_task_id = google_task_id
                    task.google_task_list_id = google_list_id
                    # Update in database to persist Google IDs
                    self.db_manager.update_task(task)

                # Update caches
                self.synced_tasks_cache[task.id] = google_task_id
                self.google_task_lists_cache[deadline.id] = google_list_id

                logger.info(f"Task '{task.title}' synced to Google Tasks")
                return True
            else:
                logger.warning(f"Failed to sync task '{task.title}' to Google Tasks")
                return False

        except Exception as e:
            logger.error(f"Error syncing single task '{task.title}': {e}")
            return False

    def _auto_sync_task_to_google(self, task: Task, operation: str = "create") -> bool:
        """
        Automatically sync task to Google Tasks if auto-sync is enabled

        Args:
            task: The task to sync
            operation: Type of operation (create, update, delete)

        Returns:
            True if sync successful or not needed, False if sync failed
        """
        if not self._should_auto_sync():
            return True  # Not an error if sync is disabled

        try:
            if operation == "delete":
                return self._delete_task_from_google(task)
            
            # For hierarchical tasks, use the hierarchy sync method
            if task.parent_task_id or self._has_subtasks(task):
                return self._sync_task_hierarchy_to_google(task)
            
            # For regular tasks, use the single task sync method
            return self._sync_single_task_to_google(task)

        except Exception as e:
            logger.error(f"Error auto-syncing task '{task.title}': {e}")
            return False
    
    def _delete_task_from_google(self, task: Task) -> bool:
        """Delete task from Google Tasks"""
        if not task.google_task_id or not task.google_task_list_id:
            return True  # Nothing to delete
        
        try:
            success = self.google_tasks_service.delete_task_from_google(task)
            if success:
                # Remove from cache
                self.synced_tasks_cache.pop(task.id, None)
                logger.info(f"Task '{task.title}' deleted from Google Tasks")
            return success
        except Exception as e:
            logger.error(f"Error deleting task '{task.title}' from Google Tasks: {e}")
            return False
    
    def _sync_all_tasks_to_google(self) -> Dict[str, Any]:
        """
        Sync all tasks to Google Tasks with duplicate prevention
        
        Returns:
            Dictionary with sync results
        """
        if not self._is_google_sync_enabled():
            return {
                "success": False,
                "error": "Google Tasks not enabled or authenticated",
                "total": 0,
                "synced": 0,
                "errors": []
            }
        
        results = {
            "success": True,
            "total": len(self.current_tasks),
            "synced": 0,
            "skipped": 0,
            "errors": [],
            "lists_created": 0,
            "lists_reused": 0
        }
        
        try:
            # Group tasks by deadline or project to avoid duplicate lists
            tasks_by_container: Dict[UUID, List[Task]] = {}
            
            # Special container ID for standalone tasks
            STANDALONE_CONTAINER_ID = "standalone"
            
            for task in self.current_tasks:
                container_id = None
                if task.deadline_id and task.deadline_id in self.current_deadlines:
                    # Task has a deadline
                    container_id = task.deadline_id
                elif task.project_id and task.project_id in self.current_projects:
                    # Task is directly attached to project
                    container_id = task.project_id
                else:
                    # Standalone task
                    container_id = STANDALONE_CONTAINER_ID
                
                if container_id not in tasks_by_container:
                    tasks_by_container[container_id] = []
                tasks_by_container[container_id].append(task)
            
            # Sync each container's tasks
            for container_id, tasks in tasks_by_container.items():
                # Determine if this is a deadline, project, or standalone container
                deadline = None
                if container_id == STANDALONE_CONTAINER_ID:
                    # Create virtual deadline for standalone tasks
                    from dataclasses import dataclass
                    from datetime import date, timedelta
                    from uuid import uuid4
                    
                    @dataclass
                    class VirtualDeadline:
                        id: UUID
                        title: str
                        project_id: Optional[UUID] = None
                        due_date: date = date.today() + timedelta(days=30)
                    
                    deadline = VirtualDeadline(
                        id=uuid4(),
                        title="Standalone Tasks",
                        project_id=None
                    )
                elif container_id in self.current_deadlines:
                    deadline = self.current_deadlines[container_id]
                elif container_id in self.current_projects:
                    # Create virtual deadline for direct project tasks
                    project = self.current_projects[container_id]
                    from dataclasses import dataclass
                    from datetime import date, timedelta
                    
                    @dataclass
                    class VirtualDeadline:
                        id: UUID
                        title: str
                        project_id: UUID
                        due_date: date = date.today() + timedelta(days=30)
                    
                    deadline = VirtualDeadline(
                        id=container_id,
                        title=f"Tasks - {project.name}",
                        project_id=container_id
                    )
                
                if not deadline:
                    for task in tasks:
                        results["errors"].append(f"Could not determine container for task '{task.title}'")
                    continue
                
                # Get or create Google Tasks list for this container
                google_list_id = self._get_or_create_google_list(deadline)
                if not google_list_id:
                    for task in tasks:
                        results["errors"].append(f"Failed to create list for '{deadline.title}'")
                    continue
                
                # Sync each task in this deadline
                for task in tasks:
                    try:
                        # Check if task is already synced to avoid duplicates
                        if self._is_task_already_synced(task, google_list_id):
                            results["skipped"] += 1
                            logger.info(f"Task '{task.title}' already synced, skipping")
                            continue
                        
                        # Sync task
                        google_task_id = self.google_tasks_service.sync_task_to_google(task, deadline)
                        if google_task_id:
                            # Update task with Google IDs
                            task.google_task_id = google_task_id
                            task.google_task_list_id = google_list_id
                            self.db_manager.update_task(task)
                            
                            # Update cache
                            self.synced_tasks_cache[task.id] = google_task_id
                            results["synced"] += 1
                            logger.info(f"Task '{task.title}' synced to Google Tasks")
                        else:
                            results["errors"].append(f"Failed to sync task '{task.title}'")
                            
                    except Exception as e:
                        error_msg = f"Error syncing task '{task.title}': {str(e)}"
                        results["errors"].append(error_msg)
                        logger.error(error_msg)
            
            # Update last sync timestamp
            self.last_sync_timestamp = datetime.now()
            
            logger.info(f"Google Tasks sync completed: {results['synced']}/{results['total']} synced, "
                       f"{results['skipped']} skipped, {len(results['errors'])} errors")
            
        except Exception as e:
            results["success"] = False
            results["error"] = str(e)
            logger.error(f"Error during Google Tasks sync: {e}")
        
        return results
    
    def _get_or_create_google_list(self, deadline: Deadline) -> Optional[str]:
        """
        Get existing or create new Google Tasks list for deadline
        
        Args:
            deadline: The deadline to create/get list for
            
        Returns:
            Google Tasks list ID or None if failed
        """
        # Check cache first
        if deadline.id in self.google_task_lists_cache:
            return self.google_task_lists_cache[deadline.id]
        
        try:
            # Check if list already exists to avoid duplicates
            existing_lists = self.google_tasks_service.get_task_lists()
            list_title = f"[AGEV] {deadline.title}"
            
            for task_list in existing_lists:
                if task_list.get('title', '').strip() == list_title.strip():
                    # Found existing list
                    list_id = task_list['id']
                    self.google_task_lists_cache[deadline.id] = list_id
                    logger.info(f"Reusing existing Google Tasks list: {list_title}")
                    return list_id
            
            # Create new list
            list_id = self.google_tasks_service.create_task_list_for_deadline(deadline)
            if list_id:
                self.google_task_lists_cache[deadline.id] = list_id
                logger.info(f"Created new Google Tasks list: {list_title}")
                return list_id
            else:
                logger.error(f"Failed to create Google Tasks list for deadline: {deadline.title}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting/creating Google Tasks list for deadline '{deadline.title}': {e}")
            return None
    
    def _is_task_already_synced(self, task: Task, google_list_id: str) -> bool:
        """
        Check if task is already synced to Google Tasks to avoid duplicates
        
        Args:
            task: The task to check
            google_list_id: The Google Tasks list ID
            
        Returns:
            True if task is already synced
        """
        try:
            # Check if task has Google IDs and they're valid
            if task.google_task_id and task.google_task_list_id:
                # Verify the task still exists on Google
                google_task = self.google_tasks_service.service.tasks().get(
                    tasklist=task.google_task_list_id,
                    task=task.google_task_id
                ).execute()
                
                if google_task:
                    # Update cache
                    self.synced_tasks_cache[task.id] = task.google_task_id
                    return True
            
            # Check if task exists by title in the specified list
            google_tasks = self.google_tasks_service.get_tasks_from_list(google_list_id)
            for google_task in google_tasks:
                if google_task.get('title', '').strip() == task.title.strip():
                    # Found duplicate, update local task with Google IDs
                    task.google_task_id = google_task['id']
                    task.google_task_list_id = google_list_id
                    self.db_manager.update_task(task)
                    self.synced_tasks_cache[task.id] = google_task['id']
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if task '{task.title}' is already synced: {e}")
            return False

    def _show_export_dialog(self):
        """Mostra il dialog per l'esportazione delle attività"""
        if not PDF_AVAILABLE:
            self._show_error_dialog("Errore", "Libreria PDF non disponibile. Installa reportlab: pip install reportlab")
            return
        
        # Opzioni di esportazione
        export_type = ft.Ref[ft.RadioGroup]()
        include_completed = ft.Ref[ft.Checkbox]()
        filter_by_deadline = ft.Ref[ft.Checkbox]()
        deadline_dropdown = ft.Ref[ft.Dropdown]()
        email_export = ft.Ref[ft.Checkbox]()
        email_address = ft.Ref[ft.TextField]()
        
        # Popola scadenze per il filtro
        deadline_options = [ft.dropdown.Option(key="all", text="Tutte le scadenze")]
        for deadline in self.current_deadlines.values():
            deadline_options.append(
                ft.dropdown.Option(key=str(deadline.id), text=deadline.title)
            )
        
        def on_deadline_filter_change(e):
            deadline_dropdown.current.disabled = not filter_by_deadline.current.value
            self.app.page.update()
        
        def on_email_change(e):
            email_address.current.disabled = not email_export.current.value
            self.app.page.update()
        
        def export_tasks(e):
            self.app.page.close(export_dialog)
            
            try:
                # Raccogli opzioni
                export_option = export_type.current.value
                include_completed_tasks = include_completed.current.value
                filter_deadline = filter_by_deadline.current.value
                selected_deadline_id = deadline_dropdown.current.value if filter_deadline else None
                send_email = email_export.current.value
                recipient_email = email_address.current.value if send_email else None
                
                # Filtra attività in base alle opzioni
                filtered_tasks = self._filter_tasks_for_export(
                    export_option, include_completed_tasks, selected_deadline_id
                )
                
                if not filtered_tasks:
                    self._show_error_dialog("Nessuna attività", "Nessuna attività trovata con i filtri selezionati.")
                    return
                
                # Genera PDF
                pdf_path = self._generate_tasks_pdf(filtered_tasks, export_option, selected_deadline_id)
                
                if send_email and recipient_email:
                    # Invia via email
                    self._send_export_email(pdf_path, recipient_email, export_option)
                else:
                    # Show success dialog with sharing options
                    self._show_export_success_dialog(pdf_path, "PDF", export_option)
                
            except Exception as ex:
                logger.error(f"Errore durante l'esportazione: {ex}")
                self._show_error_dialog("Errore", f"Errore durante l'esportazione: {str(ex)}")
        
        def close_dialog(e):
            self.app.page.close(export_dialog)
        
        export_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Row([
                ft.Icon(ft.Icons.DOWNLOAD, color=ft.Colors.GREEN_600),
                ft.Text("Esporta Attività", weight=ft.FontWeight.BOLD, size=18)
            ], alignment=ft.MainAxisAlignment.START),
            content=ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Text(
                            "Seleziona le opzioni di esportazione:", 
                            size=14, 
                            weight=ft.FontWeight.W_500,
                            color=ft.Colors.BLUE_GREY_700
                        ),
                        margin=ft.margin.only(bottom=10)
                    ),
                    
                    # Scrollable content
                    ft.Container(
                        content=ft.Column([
                            # Tipo di esportazione
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.TASK, size=20, color=ft.Colors.BLUE_600),
                                        ft.Text("Stato:", weight=ft.FontWeight.BOLD, size=16)
                                    ]),
                                    ft.Container(
                                        content=ft.RadioGroup(
                                            ref=export_type,
                                            content=ft.Column([
                                                ft.Radio(
                                                    value="active", 
                                                    label="Solo attività attive (non completate)",
                                                    label_style=ft.TextStyle(size=14)
                                                ),
                                                ft.Radio(
                                                    value="all", 
                                                    label="Tutte le attività",
                                                    label_style=ft.TextStyle(size=14)
                                                ),
                                                ft.Radio(
                                                    value="priority", 
                                                    label="Solo attività ad alta/critica priorità",
                                                    label_style=ft.TextStyle(size=14)
                                                )
                                            ], spacing=8),
                                            value="active"
                                        ),
                                        padding=ft.padding.only(left=20)
                                    )
                                ], spacing=8),
                                padding=ft.padding.all(10),
                                bgcolor=ft.Colors.BLUE_50,
                                border_radius=8
                            ),
                            
                            # Opzioni aggiuntive
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.TUNE, size=20, color=ft.Colors.ORANGE_600),
                                        ft.Text("Opzioni:", weight=ft.FontWeight.BOLD, size=16)
                                    ]),
                                    ft.Container(
                                        content=ft.Column([
                                            ft.Checkbox(
                                                ref=include_completed,
                                                label="Includi attività completate",
                                                value=False,
                                                label_style=ft.TextStyle(size=14)
                                            ),
                                            ft.Checkbox(
                                                ref=filter_by_deadline,
                                                label="Filtra per scadenza",
                                                value=False,
                                                on_change=on_deadline_filter_change,
                                                label_style=ft.TextStyle(size=14)
                                            ),
                                            ft.Container(
                                                content=ft.Dropdown(
                                                    ref=deadline_dropdown,
                                                    label="Scadenza",
                                                    options=deadline_options,
                                                    value="all",
                                                    disabled=True,
                                                    text_size=14
                                                ),
                                                padding=ft.padding.only(left=20)
                                            )
                                        ], spacing=8),
                                        padding=ft.padding.only(left=20)
                                    )
                                ], spacing=8),
                                padding=ft.padding.all(10),
                                bgcolor=ft.Colors.ORANGE_50,
                                border_radius=8
                            ),
                            
                            # Opzioni email
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.EMAIL, size=20, color=ft.Colors.GREEN_600),
                                        ft.Text("Invio:", weight=ft.FontWeight.BOLD, size=16)
                                    ]),
                                    ft.Container(
                                        content=ft.Column([
                                            ft.Checkbox(
                                                ref=email_export,
                                                label="Invia via email",
                                                value=False,
                                                on_change=on_email_change,
                                                label_style=ft.TextStyle(size=14)
                                            ),
                                            ft.Container(
                                                content=ft.TextField(
                                                    ref=email_address,
                                                    label="Indirizzo email destinatario",
                                                    hint_text="<EMAIL>",
                                                    disabled=True,
                                                    text_size=14,
                                                    prefix_icon=ft.Icons.ALTERNATE_EMAIL
                                                ),
                                                padding=ft.padding.only(left=20)
                                            )
                                        ], spacing=8),
                                        padding=ft.padding.only(left=20)
                                    )
                                ], spacing=8),
                                padding=ft.padding.all(10),
                                bgcolor=ft.Colors.GREEN_50,
                                border_radius=8
                            )
                        ], spacing=15, scroll=ft.ScrollMode.AUTO),
                        height=400,
                        padding=ft.padding.all(5)
                    )
                ], spacing=0),
                width=450,
                height=500
            ),
            actions=[
                ft.Container(
                    content=ft.Row([
                        ft.TextButton(
                            "Annulla",
                            on_click=close_dialog,
                            style=ft.ButtonStyle(
                                color=ft.Colors.GREY_600,
                                text_style=ft.TextStyle(size=14, weight=ft.FontWeight.W_500)
                            )
                        ),
                        ft.ElevatedButton(
                            "Esporta PDF",
                            icon=ft.Icons.PICTURE_AS_PDF,
                            on_click=export_tasks,
                            style=ft.ButtonStyle(
                                bgcolor=ft.Colors.GREEN_600,
                                color=ft.Colors.WHITE,
                                text_style=ft.TextStyle(size=14, weight=ft.FontWeight.BOLD),
                                elevation=3,
                                shape=ft.RoundedRectangleBorder(radius=8)
                            )
                        )
                    ], alignment=ft.MainAxisAlignment.END, spacing=10),
                    padding=ft.padding.symmetric(horizontal=10, vertical=5)
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )
        
        self.app.page.open(export_dialog)
    
    def _filter_tasks_for_export(self, export_option: str, include_completed: bool, deadline_id: str = None) -> List[Task]:
        """Filtra le attività in base alle opzioni di esportazione"""
        filtered = []
        
        for task in self.current_tasks:
            # Filtra per stato completamento
            if not include_completed and task.status == TaskStatus.COMPLETED:
                continue
            
            # Filtra per scadenza
            if deadline_id and deadline_id != "all" and str(task.deadline_id) != deadline_id:
                continue
            
            # Filtra per tipo esportazione
            if export_option == "active":
                # Solo attività non completate
                if task.status == TaskStatus.COMPLETED:
                    continue
            elif export_option == "priority":
                # Solo alta/critica priorità
                if task.priority not in [Priority.HIGH, Priority.CRITICAL]:
                    continue
            # "all" non ha filtri di stato
            
            filtered.append(task)
        
        # Ordina per priorità e data di creazione
        def get_priority_value(task):
            """Get priority value for sorting, handling both enum and string values"""
            try:
                if hasattr(task.priority, 'value'):
                    return task.priority.value
                else:
                    # If it's already a string, return it directly
                    return str(task.priority)
            except:
                return 'media'  # Default fallback

        filtered.sort(key=lambda t: (get_priority_value(t), t.created_at), reverse=True)
        return filtered
    
    def _generate_tasks_pdf(self, tasks: List[Task], export_option: str, deadline_id: str = None) -> str:
        """Genera un PDF con le attività filtrate con branding Agevolami"""
        try:
            # Import the new branded utilities
            from core.utils.pdf_utils import PDFExportUtils, PDFTableBuilder, AgevoBrandColors
            
            # Create PDF path with branding in Documents folder
            pdf_path = PDFExportUtils.create_export_pdf_path("tasks")
            
            # Create document with Agevolami styling
            doc = SimpleDocTemplate(
                pdf_path, 
                pagesize=A4,
                topMargin=0.8*inch,
                bottomMargin=0.8*inch,
                leftMargin=0.7*inch,
                rightMargin=0.7*inch
            )
            story = []
            styles = PDFExportUtils.get_branded_styles()
            
            # Build title with deadline info
            option_text = {
                "active": "Attività Attive",
                "all": "Tutte le Attività",
                "priority": "Attività Prioritarie"
            }.get(export_option, "Attività")
            
            deadline_text = ""
            if deadline_id and deadline_id != "all":
                deadline = self.current_deadlines.get(UUID(deadline_id))
                if deadline:
                    deadline_text = f" - {PDFExportUtils.truncate_text(deadline.title, 40)}"
            
            title = f"Report Attività - {option_text}{deadline_text}"
            
            # Add branded header with logo
            PDFExportUtils.add_branded_header(story, title, len(tasks), styles, "Attività")
            
            # Create branded table if tasks exist
            if tasks:
                # Define column configuration for tasks
                column_configs = [
                    {'width': 2.2, 'text_length': 25, 'wrap': True},   # Title
                    {'width': 1.3, 'text_length': 20, 'wrap': False},  # Deadline
                    {'width': 0.7, 'text_length': 10, 'wrap': False},  # Priority
                    {'width': 0.8, 'text_length': 12, 'wrap': False},  # Status
                    {'width': 0.7, 'text_length': 10, 'wrap': False},  # Created
                    {'width': 1.8, 'text_length': 35, 'wrap': True}    # Description
                ]
                
                # Create table builder
                table_builder = PDFTableBuilder(
                    ['Titolo', 'Scadenza', 'Priorità', 'Stato', 'Creata', 'Descrizione'],
                    column_configs
                )
                
                # Add data rows
                for task in tasks:
                    deadline_name = "N/A"
                    if task.deadline_id:
                        deadline = self.current_deadlines.get(task.deadline_id)
                        if deadline:
                            deadline_name = deadline.title
                    
                    priority_text = {
                        Priority.LOW: "🟢 Bassa",
                        Priority.MEDIUM: "🔵 Media", 
                        Priority.HIGH: "🟠 Alta",
                        Priority.CRITICAL: "🔴 Critica"
                    }.get(task.priority, "N/A")
                    
                    status_text = {
                        TaskStatus.PENDING: "⏳ In attesa",
                        TaskStatus.IN_PROGRESS: "🔄 In corso",
                        TaskStatus.WAITING: "⏸️ In pausa",
                        TaskStatus.COMPLETED: "✅ Completata",
                        TaskStatus.CANCELLED: "❌ Annullata"
                    }.get(task.status, "N/A")
                    
                    row_data = [
                        task.title,
                        deadline_name,
                        priority_text,
                        status_text,
                        task.created_at.strftime('%d/%m/%Y'),
                        task.description or "Nessuna descrizione"
                    ]
                    
                    table_builder.add_row(row_data)
                
                # Add table to story
                story.append(table_builder.build())
                
                # Generate enhanced statistics
                story.append(Spacer(1, 20))
                
                # Count statistics
                status_counts = {}
                priority_counts = {}
                overdue_count = 0
                completed_today = 0
                total_estimated_hours = 0
                
                for task in tasks:
                    status = task.status.value if hasattr(task.status, 'value') else str(task.status)
                    priority = task.priority.value if hasattr(task.priority, 'value') else str(task.priority)
                    
                    status_counts[status] = status_counts.get(status, 0) + 1
                    priority_counts[priority] = priority_counts.get(priority, 0) + 1
                    
                    # Check overdue tasks
                    if task.due_date and task.due_date < datetime.now() and status != "completato":
                        overdue_count += 1
                    
                    # Check completed today
                    if status == "completato" and task.updated_at and task.updated_at.date() == date.today():
                        completed_today += 1
                    
                    # Sum estimated hours
                    if task.estimated_hours:
                        total_estimated_hours += task.estimated_hours
                
                # Create statistics summary
                summary_style = ParagraphStyle(
                    'AgevoTasksSummary',
                    parent=styles['summary'],
                    textColor=AgevoBrandColors.AGEVO_GREEN
                )
                
                story.append(Paragraph("📊 RIEPILOGO STATISTICHE ATTIVITÀ", summary_style))
                
                # Build comprehensive statistics text
                summary_text = f"""
                <b><font color='#{AgevoBrandColors.AGEVO_BLUE.hexval()}'>Distribuzione per Stato:</font></b><br/>
                """
                
                for status, count in status_counts.items():
                    percentage = (count / len(tasks)) * 100
                    summary_text += f"• {status.title()}: <b>{count}</b> ({percentage:.1f}%)<br/>"
                
                summary_text += f"""<br/>
                <b><font color='#{AgevoBrandColors.AGEVO_BLUE.hexval()}'>Distribuzione per Priorità:</font></b><br/>
                """
                
                for priority, count in priority_counts.items():
                    percentage = (count / len(tasks)) * 100
                    summary_text += f"• {priority.title()}: <b>{count}</b> ({percentage:.1f}%)<br/>"
                
                summary_text += f"""<br/>
                <b><font color='#{AgevoBrandColors.AGEVO_RED.hexval()}'>Analisi Tempistiche e Produttività:</font></b><br/>
                • Attività in ritardo: <b>{overdue_count}</b><br/>
                • Completate oggi: <b>{completed_today}</b><br/>
                • Ore stimate totali: <b>{total_estimated_hours:.1f}h</b><br/>
                • Media ore per task: <b>{(total_estimated_hours/len(tasks)):.1f}h</b>
                """
                
                story.append(Paragraph(summary_text, styles['base']['Normal']))
                
            else:
                # No tasks found message with styling
                no_data_style = ParagraphStyle(
                    'NoDataStyle',
                    parent=styles['base']['Normal'],
                    fontSize=12,
                    alignment=TA_CENTER,
                    textColor=AgevoBrandColors.AGEVO_DARK_GRAY,
                    spaceAfter=20
                )
                story.append(Paragraph("📋 Nessuna attività trovata con i filtri selezionati.", no_data_style))
            
            # Add branded footer
            PDFExportUtils.add_branded_footer(story, styles)
            
            # Generate PDF
            doc.build(story)
            logger.info(f"Branded tasks PDF generated: {pdf_path}")
            return pdf_path
            
        except Exception as e:
            logger.error(f"Error generating branded tasks PDF: {e}")
            # Fallback to original method if branded version fails
            return self._generate_tasks_pdf_fallback(tasks, export_option, deadline_id)
    
    def _generate_tasks_pdf_fallback(self, tasks: List[Task], export_option: str, deadline_id: str = None) -> str:
        """Fallback PDF generation method (original implementation)"""
        # Create file in Documents folder
        from core.utils.pdf_utils import PDFExportUtils
        export_dir = PDFExportUtils.get_export_directory()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"tasks_export_{timestamp}.pdf"
        pdf_path = os.path.join(export_dir, filename)
        
        # Crea documento PDF
        doc = SimpleDocTemplate(pdf_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Helper function to truncate and wrap long text
        def truncate_text(text: str, max_length: int) -> str:
            """Truncate text if too long, add ellipsis"""
            if not text:
                return "N/A"
            if len(text) <= max_length:
                return text
            return text[:max_length-3] + "..."
        
        def wrap_text_for_cell(text: str, max_length: int) -> str:
            """Wrap text for table cells with line breaks"""
            if not text:
                return "N/A"
            if len(text) <= max_length:
                return text
            
            # Split text into chunks
            chunks = []
            words = text.split()
            current_chunk = ""
            
            for word in words:
                if len(current_chunk + " " + word) <= max_length:
                    current_chunk += (" " + word) if current_chunk else word
                else:
                    if current_chunk:
                        chunks.append(current_chunk)
                    current_chunk = word
            
            if current_chunk:
                chunks.append(current_chunk)
            
            # If too many chunks, truncate and add ellipsis
            if len(chunks) > 3:
                chunks = chunks[:3]
                chunks[-1] += "..."
            
            return "<br/>".join(chunks)
        
        # Stile personalizzato per il titolo
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        # Stile per celle tabella con text wrapping
        cell_style = ParagraphStyle(
            'CellStyle',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_LEFT,
            wordWrap='CJK',
            allowWidows=1,
            allowOrphans=1
        )
        
        # Titolo
        option_text = {
            "active": "Attività Attive",
            "all": "Tutte le Attività",
            "priority": "Attività Prioritarie"
        }.get(export_option, "Attività")
        
        deadline_text = ""
        if deadline_id and deadline_id != "all":
            deadline = self.current_deadlines.get(UUID(deadline_id))
            if deadline:
                # Truncate long deadline titles in the report title
                deadline_text = f" - {truncate_text(deadline.title, 40)}"
        
        title = f"Report Attività - {option_text}{deadline_text}"
        story.append(Paragraph(title, title_style))
        
        # Informazioni generali
        info_text = f"Generato il: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}<br/>"
        info_text += f"Totale attività: {len(tasks)}"
        story.append(Paragraph(info_text, styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Tabella attività
        if tasks:
            # Header tabella
            data = [['Titolo', 'Scadenza', 'Priorità', 'Stato', 'Creata', 'Descrizione']]
            
            # Dati attività
            for task in tasks:
                deadline_name = "N/A"
                if task.deadline_id:
                    deadline = self.current_deadlines.get(task.deadline_id)
                    if deadline:
                        # Truncate long deadline names
                        deadline_name = truncate_text(deadline.title, 20)
                
                priority_text = {
                    Priority.LOW: "Bassa",
                    Priority.MEDIUM: "Media", 
                    Priority.HIGH: "Alta",
                    Priority.CRITICAL: "Critica"
                }.get(task.priority, "N/A")
                
                status_text = {
                    TaskStatus.PENDING: "In attesa",
                    TaskStatus.IN_PROGRESS: "In corso",
                    TaskStatus.WAITING: "In pausa",
                    TaskStatus.COMPLETED: "Completata",
                    TaskStatus.CANCELLED: "Annullata"
                }.get(task.status, "N/A")
                
                # Optimize text for better PDF layout
                task_title = wrap_text_for_cell(task.title, 25)
                description = wrap_text_for_cell(task.description or "N/A", 35)
                
                # Create Paragraph objects for cells that need text wrapping
                title_para = Paragraph(task_title, cell_style)
                deadline_para = Paragraph(deadline_name, cell_style)
                desc_para = Paragraph(description, cell_style)
                
                data.append([
                    title_para,
                    deadline_para,
                    priority_text,
                    status_text,
                    task.created_at.strftime('%d/%m/%Y'),
                    desc_para
                ])
            
            # Crea tabella con colonne ottimizzate
            # Adjusted column widths for better layout
            table = Table(data, colWidths=[2.2*inch, 1.3*inch, 0.7*inch, 0.8*inch, 0.7*inch, 1.8*inch])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),  # Left align for better readability
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),  # Top align for multi-line cells
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                ('TOPPADDING', (0, 1), (-1, -1), 4),
                ('BOTTOMPADDING', (0, 1), (-1, -1), 4),
                ('LEFTPADDING', (0, 0), (-1, -1), 4),
                ('RIGHTPADDING', (0, 0), (-1, -1), 4),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                # Add row striping for better readability
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.white])
            ]))
            
            story.append(table)
        else:
            story.append(Paragraph("Nessuna attività trovata con i filtri selezionati.", styles['Normal']))
        
        # Generate summary statistics
        if tasks:
            story.append(Spacer(1, 20))
            
            # Count by status
            status_counts = {}
            priority_counts = {}
            
            for task in tasks:
                status = task.status.value if hasattr(task.status, 'value') else str(task.status)
                priority = task.priority.value if hasattr(task.priority, 'value') else str(task.priority)
                
                status_counts[status] = status_counts.get(status, 0) + 1
                priority_counts[priority] = priority_counts.get(priority, 0) + 1
            
            # Summary section
            summary_style = ParagraphStyle(
                'SummaryStyle',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=10,
                textColor=colors.darkblue
            )
            
            story.append(Paragraph("Riepilogo Statistiche", summary_style))
            
            # Status summary
            summary_text = "<b>Distribuzione per Stato:</b><br/>"
            for status, count in status_counts.items():
                summary_text += f"• {status.title()}: {count}<br/>"
            
            summary_text += "<br/><b>Distribuzione per Priorità:</b><br/>"
            for priority, count in priority_counts.items():
                summary_text += f"• {priority.title()}: {count}<br/>"
            
            story.append(Paragraph(summary_text, styles['Normal']))
        
        # Genera PDF
        doc.build(story)
        logger.info(f"Fallback PDF generato: {pdf_path}")
        return pdf_path
    
    def _send_export_email(self, pdf_path: str, recipient_email: str, export_option: str):
        """Invia il PDF via email"""
        try:
            # Load current settings
            import json
            from pathlib import Path

            settings_file = Path('data/settings.json')
            email_settings = {}

            if settings_file.exists():
                try:
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        email_settings = settings.get('email', {})
                except Exception as e:
                    logger.error(f"Error loading email settings: {e}")

            # Check if SMTP is configured
            if not email_settings.get('server') or not email_settings.get('username'):
                raise Exception("SMTP non configurato. Vai alle Impostazioni per configurarlo.")

            # Initialize email service
            from core.config.app_config import AppConfig
            config = AppConfig()
            email_service = EmailService(config)

            # Update email service with current settings
            email_service.smtp_config.update({
                'smtp_server': email_settings.get('server', ''),
                'smtp_port': email_settings.get('port', 587),
                'smtp_username': email_settings.get('username', ''),
                'smtp_password': email_settings.get('password', ''),
                'smtp_use_tls': email_settings.get('use_tls', True),
                'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                'from_email': email_settings.get('sender_email', email_settings.get('username', '')),
                'enabled': True
            })

            # Test connection first
            if not email_service.test_connection():
                raise Exception("Impossibile connettersi al server SMTP. Verifica le impostazioni.")

            option_text = {
                "active": "attività attive",
                "all": "tutte le attività",
                "priority": "attività prioritarie"
            }.get(export_option, "attività")

            subject = f"Report Attività - {option_text.title()}"
            body = f"""Gentile utente,

In allegato trovi il report delle attività per {option_text}.

Il report è stato generato automaticamente dal sistema Agevolami PM.

Cordiali saluti,
Team Agevolami"""

            html_body = f"""<html><body>
<h2>Report Attività</h2>
<p>Gentile utente,</p>
<p>In allegato trovi il report delle attività per <strong>{option_text}</strong>.</p>
<p>Il report è stato generato automaticamente dal sistema Agevolami PM.</p>
<br>
<p>Cordiali saluti,<br>Team Agevolami</p>
</body></html>"""

            # Convert pdf_path string to Path object for attachments
            from pathlib import Path
            pdf_file = Path(pdf_path)

            success = email_service.send_email(
                to_email=recipient_email,
                subject=subject,
                body=body,
                html_body=html_body,
                attachments=[pdf_file]
            )

            if success:
                logger.info(f"Report inviato via email a: {recipient_email}")
            else:
                raise Exception("Invio email fallito")

        except Exception as e:
            logger.error(f"Errore invio email: {e}")
            raise e
    
    def _show_export_success_dialog(self, filename: str, format_type: str, export_option: str):
        """Show export success dialog with sharing options including SMTP"""
        import os
        import subprocess
        import urllib.parse
        from datetime import datetime
        
        def close_dialog(e):
            self.app.page.close(dialog)
        
        def open_file_location(e):
            """Open file location in file explorer"""
            try:
                folder_path = os.path.dirname(filename)
                if os.name == 'nt':  # Windows
                    try:
                        subprocess.run(['explorer', '/select,', filename], check=False)
                    except Exception:
                        subprocess.run(['explorer', folder_path], check=False)
                elif os.name == 'posix':  # macOS/Linux
                    if os.uname().sysname == 'Darwin':  # macOS
                        subprocess.run(['open', '-R', filename], check=False)
                    else:  # Linux
                        subprocess.run(['xdg-open', folder_path], check=False)
                close_dialog(e)
            except Exception as ex:
                logger.error(f"Error opening file location: {ex}")
                self._show_error_dialog("Errore", f"Impossibile aprire la cartella: {str(ex)}")
        
        def copy_file_path(e):
            """Copy file path to clipboard"""
            try:
                import pyperclip
                pyperclip.copy(filename)
                self._show_success_dialog("Successo", "Percorso file copiato negli appunti")
                close_dialog(e)
            except ImportError:
                self._show_error_dialog("Errore", "Installa 'pyperclip' per copiare il percorso")
            except Exception as ex:
                logger.error(f"Error copying to clipboard: {ex}")
        
        def share_via_email(e):
            """Share file via system email client"""
            try:
                if os.name == 'nt':  # Windows
                    try:
                        import win32api
                        import win32con
                        
                        subject = f"Export Attività - {format_type}"
                        body = f"Ciao,\n\nTi invio l'export delle attività da Agevolami PM.\n\nFile allegato: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                        
                        win32api.ShellExecute(
                            0,
                            'open',
                            'mailto:',
                            f'subject={subject}&body={body}&attach={filename}',
                            '',
                            win32con.SW_SHOWNORMAL
                        )
                        
                    except ImportError:
                        try:
                            subprocess.run(['explorer', '/select,', filename], check=False)
                        except Exception:
                            subprocess.run(['explorer', os.path.dirname(filename)], check=False)
                        subject = f"Export Attività - {format_type}"
                        body = f"Ciao,\n\nTi invio l'export delle attività da Agevolami PM.\n\nFile: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                        mailto_url = f"mailto:?subject={urllib.parse.quote(subject)}&body={urllib.parse.quote(body)}"
                        os.startfile(mailto_url)
                
                else:  # macOS/Linux
                    folder_path = os.path.dirname(filename)
                    subject = f"Export Attività - {format_type}"
                    body = f"Ciao,\n\nTi invio l'export delle attività da Agevolami PM.\n\nFile: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                    mailto_url = f"mailto:?subject={urllib.parse.quote(subject)}&body={urllib.parse.quote(body)}"
                    
                    if os.uname().sysname == 'Darwin':  # macOS
                        subprocess.run(['open', '-R', filename], check=False)
                        subprocess.run(['open', mailto_url])
                    else:  # Linux
                        subprocess.run(['xdg-open', folder_path], check=False)
                        subprocess.run(['xdg-open', mailto_url])
                
                close_dialog(e)
                
            except Exception as ex:
                logger.error(f"Error opening email client: {ex}")
                self._show_error_dialog("Errore", f"Errore apertura email: {str(ex)}")
        
        # Get file info
        file_size = os.path.getsize(filename) if os.path.exists(filename) else 0
        file_size_mb = file_size / (1024 * 1024)
        
        option_text = {
            "active": "Attività Attive",
            "all": "Tutte le Attività",
            "priority": "Attività Prioritarie"
        }.get(export_option, "Attività")
        
        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Row([
                ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN_600, size=24),
                ft.Text("Export Completato!", weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600)
            ], spacing=8),
            content=ft.Container(
                content=ft.Column([
                    # File info
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.DESCRIPTION, size=16, color=ft.Colors.BLUE_600),
                                ft.Text("Informazioni File", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                            ], spacing=6),
                            ft.Text(f"Nome: {os.path.basename(filename)}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Formato: {format_type}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Dimensione: {file_size_mb:.2f} MB", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Contenuto: {option_text}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Percorso: {filename}", size=10, color=ft.Colors.GREY_500, selectable=True)
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),
                    
                    ft.Container(height=10),
                    
                    # Action buttons
                    ft.Text("Azioni Disponibili:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_700),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.FOLDER_OPEN, size=16),
                                ft.Text("Apri Cartella", size=12)
                            ], spacing=4, tight=True),
                            on_click=open_file_location,
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.COPY, size=16),
                                ft.Text("Copia Percorso", size=12)
                            ], spacing=4, tight=True),
                            on_click=copy_file_path,
                            bgcolor=ft.Colors.GREY_600,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=8),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.EMAIL, size=16),
                                ft.Text("Condividi Email", size=12)
                            ], spacing=4, tight=True),
                            on_click=share_via_email,
                            bgcolor=ft.Colors.GREEN_600,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.SEND, size=16),
                                ft.Text("SMTP Email", size=12)
                            ], spacing=4, tight=True),
                            on_click=lambda e: self._show_smtp_email_dialog(filename, format_type, export_option),
                            bgcolor=ft.Colors.INDIGO_600,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=8)
                ], spacing=8),
                width=500
            ),
            actions=[
                ft.TextButton("Chiudi", on_click=close_dialog)
            ]
        )
        
        self.app.page.open(dialog)
    
    def _show_smtp_email_dialog(self, filename: str, format_type: str, export_option: str):
        """Show SMTP email dialog for direct email sending"""
        import os
        from datetime import datetime
        
        # References for form fields
        recipient_ref = ft.Ref[ft.TextField]()
        subject_ref = ft.Ref[ft.TextField]()
        message_ref = ft.Ref[ft.TextField]()
        
        def close_dialog(e):
            self.app.page.close(smtp_dialog)
        
        def send_via_smtp(e):
            """Send email via SMTP with attachment"""
            try:
                recipient = recipient_ref.current.value
                subject = subject_ref.current.value
                message = message_ref.current.value
                
                # Basic validation
                if not recipient or not subject:
                    self._show_error_dialog("Errore", "Destinatario e oggetto sono obbligatori")
                    return
                
                # Validate email format
                import re
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, recipient):
                    self._show_error_dialog("Errore", "Formato email non valido")
                    return
                
                # Load current settings
                import json
                from pathlib import Path
                
                settings_file = Path('data/settings.json')
                email_settings = {}
                
                if settings_file.exists():
                    try:
                        with open(settings_file, 'r', encoding='utf-8') as f:
                            settings = json.load(f)
                            email_settings = settings.get('email', {})
                    except Exception as e:
                        logger.error(f"Error loading email settings: {e}")
                
                # Check if SMTP is configured
                if not email_settings.get('server') or not email_settings.get('username'):
                    self._show_error_dialog("Errore", "SMTP non configurato. Vai alle Impostazioni per configurarlo.")
                    return
                
                # Initialize email service
                from core.config.app_config import AppConfig
                config = AppConfig()
                email_service = EmailService(config)
                
                # Update email service with current settings
                email_service.smtp_config.update({
                    'smtp_server': email_settings.get('server', ''),
                    'smtp_port': email_settings.get('port', 587),
                    'smtp_username': email_settings.get('username', ''),
                    'smtp_password': email_settings.get('password', ''),
                    'smtp_use_tls': email_settings.get('use_tls', True),
                    'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                    'from_email': email_settings.get('sender_email', email_settings.get('username', '')),
                    'enabled': True
                })
                
                # Test connection first
                if not email_service.test_connection():
                    self._show_error_dialog("Errore", "Impossibile connettersi al server SMTP. Verifica le impostazioni.")
                    return
                
                # Send email with attachment
                success = email_service.send_email_with_attachment(
                    to_email=recipient,
                    subject=subject,
                    body=message,
                    attachment_path=filename
                )
                
                if success:
                    # Close dialog and show success
                    close_dialog(e)
                    self._show_success_dialog("Successo", f"Email inviata con successo a {recipient}")
                    logger.info(f"SMTP email sent successfully to {recipient}")
                else:
                    self._show_error_dialog("Errore", "Errore durante l'invio dell'email")
                    
            except Exception as ex:
                logger.error(f"SMTP email error: {ex}")
                self._show_error_dialog("Errore", f"Errore SMTP: {str(ex)}")
        
        # Get file info
        file_size = os.path.getsize(filename) if os.path.exists(filename) else 0
        file_size_mb = file_size / (1024 * 1024)
        
        option_text = {
            "active": "attività attive",
            "all": "tutte le attività",
            "priority": "attività prioritarie"
        }.get(export_option, "attività")
        
        # Create default email content
        default_subject = f"Export Attività - {option_text.title()} - {datetime.now().strftime('%d/%m/%Y')}"
        default_message = f"""Ciao,

Ti invio l'export delle attività per {option_text} da Agevolami PM.

Dettagli file:
- Nome: {os.path.basename(filename)}
- Formato: {format_type}
- Dimensione: {file_size_mb:.2f} MB
- Contenuto: {option_text.title()}
- Generato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}

Il file è allegato a questa email.

Cordiali saluti,
Agevolami PM"""
        
        # Create dialog
        smtp_dialog = ft.AlertDialog(
            title=ft.Row([
                ft.Icon(ft.Icons.SEND, color=ft.Colors.INDIGO_600, size=24),
                ft.Text("Invia Email SMTP", weight=ft.FontWeight.BOLD, color=ft.Colors.INDIGO_600)
            ], spacing=8),
            content=ft.Container(
                content=ft.Column([
                    # File info
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.ATTACH_FILE, size=16, color=ft.Colors.BLUE_600),
                                ft.Text("File Allegato", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                            ], spacing=6),
                            ft.Text(f"Nome: {os.path.basename(filename)}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Formato: {format_type}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Dimensione: {file_size_mb:.2f} MB", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Contenuto: {option_text.title()}", size=12, color=ft.Colors.GREY_700),
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),
                    
                    ft.Container(height=10),
                    
                    # Email form
                    ft.Text("Componi Email:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_700),
                    
                    ft.TextField(
                        ref=recipient_ref,
                        label="Destinatario Email *",
                        hint_text="<EMAIL>",
                        prefix_icon=ft.Icons.PERSON,
                        text_size=14,
                        border_color=ft.Colors.INDIGO_200,
                        focused_border_color=ft.Colors.INDIGO_600
                    ),
                    
                    ft.TextField(
                        ref=subject_ref,
                        label="Oggetto *",
                        value=default_subject,
                        prefix_icon=ft.Icons.SUBJECT,
                        text_size=14,
                        border_color=ft.Colors.INDIGO_200,
                        focused_border_color=ft.Colors.INDIGO_600
                    ),
                    
                    ft.TextField(
                        ref=message_ref,
                        label="Messaggio",
                        value=default_message,
                        prefix_icon=ft.Icons.MESSAGE,
                        text_size=14,
                        multiline=True,
                        max_lines=8,
                        border_color=ft.Colors.INDIGO_200,
                        focused_border_color=ft.Colors.INDIGO_600
                    ),
                    
                    # Info note
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.INFO, size=16, color=ft.Colors.ORANGE_600),
                            ft.Text("Utilizza le impostazioni SMTP configurate nell'applicazione", 
                                   size=12, color=ft.Colors.ORANGE_600, expand=True)
                        ], spacing=6),
                        padding=ft.padding.all(8),
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=6,
                        border=ft.border.all(1, ft.Colors.ORANGE_200)
                    )
                ], spacing=8, scroll=ft.ScrollMode.AUTO),
                width=500,
                height=500
            ),
            actions=[
                ft.Row([
                    ft.TextButton(
                        "Annulla",
                        on_click=close_dialog,
                        style=ft.ButtonStyle(color=ft.Colors.GREY_600)
                    ),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.SEND, size=16),
                            ft.Text("Invia Email", size=12)
                        ], spacing=4, tight=True),
                        on_click=send_via_smtp,
                        bgcolor=ft.Colors.INDIGO_600,
                        color=ft.Colors.WHITE
                    )
                ], alignment=ft.MainAxisAlignment.END, spacing=10)
            ]
        )
        
        self.app.page.open(smtp_dialog)
    
    def refresh_data(self):
        """Refresh task data from database with caching"""
        try:
            # Reset data loaded flag to force fresh data
            self._data_loaded = False

            # Load tasks, deadlines, and projects from database using cache
            from core.services.data_cache_manager import get_data_cache
            data_cache = get_data_cache(self.db_manager)

            self.current_tasks = data_cache.get_all_tasks()
            self.current_deadlines = {d.id: d for d in data_cache.get_all_deadlines()}
            self.current_projects = {p.id: p for p in data_cache.get_all_projects()}

            # Refresh Google Tasks authentication status
            self._refresh_google_tasks_status()

            self._data_loaded = True
            logger.info(f"Refreshed data: {len(self.current_tasks)} tasks, {len(self.current_deadlines)} deadlines, {len(self.current_projects)} projects")

            # Only trigger UI refresh if we're not being called from the layout's _load_view
            # (to avoid double refresh)
            if not hasattr(self, '_refreshing_from_layout'):
                self._refresh_task_list()

        except Exception as e:
            logger.error(f"Error loading tasks: {e}")
            # Fallback to empty lists if database fails
            self.current_tasks = []
            self.current_deadlines = {}
            self.current_projects = {}
            self._data_loaded = True
            # Still update UI even with empty data (but only if not from layout)
            if not hasattr(self, '_refreshing_from_layout'):
                self._refresh_task_list()

    def _refresh_google_tasks_status(self):
        """Refresh Google Tasks authentication status in settings"""
        try:
            # Ensure settings view is initialized
            if not hasattr(self.app, 'settings_view'):
                logger.debug("Settings view not initialized, creating it...")
                from ui.views.settings import SettingsView
                self.app.settings_view = SettingsView(self.app)

            if hasattr(self.app.settings_view, 'settings'):
                # Update the authentication status based on current service state
                current_enabled = self.google_tasks_service.is_enabled()
                current_authenticated = self.google_tasks_service.is_authenticated()

                # Ensure google_services section exists
                if 'google_services' not in self.app.settings_view.settings:
                    self.app.settings_view.settings['google_services'] = {}

                self.app.settings_view.settings['google_services']['tasks_enabled'] = current_enabled
                self.app.settings_view.settings['google_services']['tasks_authenticated'] = current_authenticated

                # Save the updated settings
                self.app.settings_view._save_settings()

                logger.info(f"Refreshed Google Tasks status - Enabled: {current_enabled}, Authenticated: {current_authenticated}")
            else:
                logger.warning("Settings view exists but settings not found")

        except Exception as e:
            logger.error(f"Error refreshing Google Tasks status: {e}")
    
    def build(self) -> ft.Container:
        """Build the tasks view"""
        # Always ensure we have current data for building UI components
        if not hasattr(self, '_data_loaded') or not self._data_loaded:
            self._load_data_only()
        
        return ft.Container(
            content=ft.Column([
                self._create_header(),
                self._create_filters(),
                self._create_task_list()
            ], spacing=16),
            padding=ft.padding.all(20),
            expand=True,
            bgcolor=ft.Colors.GREY_50
        )
    
    def _load_data_only(self):
        """Load data without triggering UI refresh using cache"""
        try:
            # Load tasks, deadlines, and projects from database using cache
            from core.services.data_cache_manager import get_data_cache
            data_cache = get_data_cache(self.db_manager)

            self.current_tasks = data_cache.get_all_tasks()
            self.current_deadlines = {d.id: d for d in data_cache.get_all_deadlines()}
            self.current_projects = {p.id: p for p in data_cache.get_all_projects()}
            
            self._data_loaded = True
            logger.info(f"Loaded {len(self.current_tasks)} tasks, {len(self.current_deadlines)} deadlines, {len(self.current_projects)} projects")
            
        except Exception as e:
            logger.error(f"Error loading tasks: {e}")
            # Fallback to empty lists if database fails
            self.current_tasks = []
            self.current_deadlines = {}
            self.current_projects = {}
            self._data_loaded = True 